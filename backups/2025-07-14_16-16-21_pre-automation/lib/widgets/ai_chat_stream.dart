import 'package:dasso_reader/enums/ai_role.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/models/ai_message.dart';
import 'package:dasso_reader/providers/ai_chat.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';
import 'package:dasso_reader/utils/toast/common.dart';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/services.dart';

class AiChatStream extends ConsumerStatefulWidget {
  const AiChatStream({
    super.key,
    this.initialMessage,
    this.sendImmediate = false,
  });

  final String? initialMessage;
  final bool sendImmediate;

  @override
  ConsumerState<AiChatStream> createState() => AiChatStreamState();
}

class AiChatStreamState extends ConsumerState<AiChatStream> {
  final TextEditingController inputController = TextEditingController();
  Stream<List<AiMessage>>? _messageStream;
  final ScrollController _scrollController = ScrollController();

  List<Map<String, String>> _getQuickPrompts(BuildContext context) {
    return [
      {
        'label': L10n.of(context).ai_quick_prompt_examples,
        'prompt': L10n.of(context).ai_quick_prompt_examples_text,
      },
      {
        'label': L10n.of(context).ai_quick_prompt_breakdown,
        'prompt': L10n.of(context).ai_quick_prompt_breakdown_text,
      },
      {
        'label': L10n.of(context).ai_quick_prompt_usage,
        'prompt': L10n.of(context).ai_quick_prompt_usage_text,
      },
      {
        'label': L10n.of(context).ai_quick_prompt_synonyms,
        'prompt': L10n.of(context).ai_quick_prompt_synonyms_text,
      },
      {
        'label': L10n.of(context).ai_quick_prompt_mnemonics,
        'prompt': L10n.of(context).ai_quick_prompt_mnemonics_text,
      },
      {
        'label': L10n.of(context).ai_quick_prompt_grammar,
        'prompt': L10n.of(context).ai_quick_prompt_grammar_text,
      },
    ];
  }

  @override
  void initState() {
    super.initState();
    inputController.text = widget.initialMessage ?? '';
    if (widget.sendImmediate) {
      _sendMessage();
    }
    _scrollToBottom();
  }

  @override
  void dispose() {
    inputController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _sendMessage({bool isRegenerate = false}) {
    if (inputController.text.trim().isEmpty) return;

    final message = inputController.text.trim();
    inputController.clear();

    setState(() {
      _messageStream = ref.read(aiChatProvider.notifier).sendMessageStream(
            message,
            ref,
            isRegenerate,
          );
    });
  }

  void _useQuickPrompt(String prompt) {
    if (prompt == L10n.of(context).ai_quick_prompt_grammar_text) {
      final selectedText = inputController.text.trim();
      if (selectedText.isEmpty) {
        inputController.text = prompt;
      } else {
        inputController.text =
            'Analyze the grammar in this Chinese text:\n\n$selectedText\n\nProvide a detailed analysis with: grammar point identification, explanation in English and Arabic, pinyin with tone marks, translations, and key usage notes.';
      }
    } else {
      inputController.text = '$prompt "${inputController.text}"';
    }
    _sendMessage();
  }

  void _clearMessage() {
    setState(() {
      ref.read(aiChatProvider.notifier).clear();
      _messageStream = null;
    });
  }

  void _regenerateLastMessage() {
    final messages = ref.read(aiChatProvider).value;
    if (messages != null && messages.isNotEmpty) {
      for (int i = messages.length - 1; i >= 0; i--) {
        if (messages[i].role == AiRole.user) {
          final userMessage = messages[i].content;
          ref.read(aiChatProvider.notifier).clear();
          for (int j = 0; j < i; j++) {
            ref.read(aiChatProvider.notifier).sendMessage(messages[j].content);
          }
          setState(() {
            inputController.text = userMessage;
            _sendMessage(isRegenerate: true);
          });
          break;
        }
      }
    }
  }

  void _copyMessageContent(String content) {
    Clipboard.setData(ClipboardData(text: content));
    AnxToast.show(L10n.of(context).notes_page_copied);
  }

  AiMessage? _getLastAssistantMessage() {
    final messages = ref.watch(aiChatProvider).asData?.value;
    if (messages == null || messages.isEmpty) return null;

    for (int i = messages.length - 1; i >= 0; i--) {
      if (messages[i].role == AiRole.assistant) {
        return messages[i];
      }
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final quickPrompts = _getQuickPrompts(context);

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Column(
        children: [
          Expanded(
            child: _messageStream != null
                ? StreamBuilder<List<AiMessage>>(
                    stream: _messageStream,
                    builder: (context, snapshot) {
                      if (!snapshot.hasData) {
                        return const Center(
                          child: CircularProgressIndicator(),
                        );
                      }

                      final messages = snapshot.data!;
                      _scrollToBottom();

                      return ListView.builder(
                        controller: _scrollController,
                        itemCount: messages.length,
                        itemBuilder: (context, index) {
                          final message = messages[index];
                          return _buildMessageItem(message);
                        },
                      );
                    },
                  )
                : ref.watch(aiChatProvider).when(
                      data: (messages) {
                        if (messages.isEmpty) {
                          return Center(
                            child: Text(L10n.of(context).ai_hint_text),
                          );
                        }

                        return ListView.builder(
                          controller: _scrollController,
                          itemCount: messages.length,
                          itemBuilder: (context, index) {
                            final message = messages[index];
                            return _buildMessageItem(message);
                          },
                        );
                      },
                      loading: () =>
                          const Center(child: CircularProgressIndicator()),
                      error: (error, stack) =>
                          Center(child: Text('error: $error')),
                    ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: quickPrompts.map((prompt) {
                  return Padding(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: ActionChip(
                      label: Text(prompt['label']!),
                      onPressed: () => _useQuickPrompt(prompt['prompt']!),
                      // MD3 compliant state layers for chips
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      visualDensity: VisualDensity.compact,
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                SemanticHelpers.button(
                  context: context,
                  label: 'Clear message',
                  hint: 'Clears the current input message',
                  onTap: _clearMessage,
                  child: IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: _clearMessage,
                  ),
                ),
                Expanded(
                  child: SemanticHelpers.textField(
                    label: 'AI chat input',
                    hint: 'Type your message to the AI assistant',
                    child: TextField(
                      controller: inputController,
                      decoration: InputDecoration(
                        isDense: true,
                        hintText: L10n.of(context).ai_hint_input_placeholder,
                        border: const OutlineInputBorder(
                          borderRadius: BorderRadius.all(Radius.circular(30)),
                        ),
                      ),
                      maxLines: null,
                      textInputAction: TextInputAction.send,
                      onSubmitted: (_) => _sendMessage(),
                    ),
                  ),
                ),
                SemanticHelpers.button(
                  context: context,
                  label: 'Send message',
                  hint: 'Sends your message to the AI assistant',
                  onTap: _sendMessage,
                  child: IconButton(
                    icon: const Icon(Icons.send),
                    onPressed: _sendMessage,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageItem(AiMessage message) {
    final isUser = message.role == AiRole.user;
    final isLongMessage = message.content.length > 300;
    final lastAssistantMessage = _getLastAssistantMessage();

    return Padding(
      padding: EdgeInsets.only(
        bottom: 8.0,
        left: isUser ? 8.0 : 0,
        right: isUser ? 0 : 8.0,
      ),
      child: Row(
        mainAxisAlignment:
            isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(width: 8),
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isUser
                    ? Theme.of(context).colorScheme.primaryContainer
                    : Theme.of(context).colorScheme.surfaceContainer,
                borderRadius: BorderRadius.only(
                  topLeft: isUser ? const Radius.circular(12) : Radius.zero,
                  topRight: isUser ? Radius.zero : const Radius.circular(12),
                  bottomLeft: isUser ? Radius.zero : const Radius.circular(12),
                  bottomRight: isUser ? const Radius.circular(12) : Radius.zero,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  isUser
                      ? _buildCollapsibleText(
                          message.content,
                          isLongMessage,
                        )
                      : _buildCollapsibleMarkdown(message.content, false),
                  if (!isUser)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        if (message == lastAssistantMessage)
                          TextButton(
                            onPressed: _regenerateLastMessage,
                            child: Text(L10n.of(context).ai_regenerate),
                          ),
                        TextButton(
                          onPressed: () => _copyMessageContent(message.content),
                          child: Text(L10n.of(context).common_copy),
                        ),
                      ],
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 8),
        ],
      ),
    );
  }

  Widget _buildCollapsibleText(String text, bool isLongMessage) {
    if (!isLongMessage) {
      return SelectableText(
        text,
        selectionControls: MaterialTextSelectionControls(),
      );
    }

    return _CollapsibleText(text: text);
  }

  Widget _buildCollapsibleMarkdown(String markdownText, bool isLongMessage) {
    if (!isLongMessage) {
      return MarkdownBody(
        data: markdownText,
        selectable: true,
      );
    }

    return _CollapsibleMarkdown(markdownText: markdownText);
  }
}

class _CollapsibleText extends StatefulWidget {
  final String text;

  const _CollapsibleText({required this.text});

  @override
  State<_CollapsibleText> createState() => _CollapsibleTextState();
}

class _CollapsibleTextState extends State<_CollapsibleText> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_isExpanded)
          SelectableText(
            widget.text,
            selectionControls: MaterialTextSelectionControls(),
          )
        else
          Stack(
            children: [
              SelectableText(
                widget.text.substring(0, 300),
                selectionControls: MaterialTextSelectionControls(),
              ),
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 40,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Theme.of(context)
                            .colorScheme
                            .primaryContainer
                            .withValues(alpha: 0),
                        Theme.of(context).colorScheme.primaryContainer,
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        TextButton(
          onPressed: () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
          },
          child: Text(
            _isExpanded
                ? L10n.of(context).ai_hint_collapse
                : L10n.of(context).ai_hint_expand,
          ),
        ),
      ],
    );
  }
}

class _CollapsibleMarkdown extends StatefulWidget {
  final String markdownText;

  const _CollapsibleMarkdown({required this.markdownText});

  @override
  State<_CollapsibleMarkdown> createState() => _CollapsibleMarkdownState();
}

class _CollapsibleMarkdownState extends State<_CollapsibleMarkdown> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_isExpanded)
          MarkdownBody(
            data: widget.markdownText,
            selectable: true,
          )
        else
          Stack(
            children: [
              MarkdownBody(
                data: widget.markdownText.substring(0, 300),
                selectable: true,
              ),
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 40,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Theme.of(context)
                            .colorScheme
                            .surfaceContainer
                            .withValues(alpha: 0),
                        Theme.of(context).colorScheme.surfaceContainer,
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        TextButton(
          onPressed: () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
          },
          child: Text(
            _isExpanded
                ? L10n.of(context).ai_hint_collapse
                : L10n.of(context).ai_hint_expand,
          ),
        ),
      ],
    );
  }
}
