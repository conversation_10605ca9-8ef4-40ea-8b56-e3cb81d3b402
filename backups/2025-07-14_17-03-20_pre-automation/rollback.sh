#!/bin/bash
# DassoShu Reader - Rollback Design System Automation
# Generated: 2025-07-14 17:03:36.707587

cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-03-20_pre-automation/lib/config/app_typography.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/config/app_typography.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-03-20_pre-automation/lib/widgets/settings/user_profile_section.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/widgets/settings/user_profile_section.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-03-20_pre-automation/lib/widgets/common/orientation_aware_widgets.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/widgets/common/orientation_aware_widgets.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-03-20_pre-automation/lib/widgets/dictionary/context_menu_character_tab.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/widgets/dictionary/context_menu_character_tab.dart'

echo 'Rollback completed successfully!'
