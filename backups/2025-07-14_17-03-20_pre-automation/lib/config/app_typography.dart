import 'package:flutter/material.dart';
import 'design_system.dart';

/// Comprehensive Typography System for the Dasso Reader App
///
/// This class provides a complete typography system using NotoSansSC font:
/// - NotoSansSC: Modern, clean font optimized for Chinese character rendering
/// - Ideal for both UI elements and reading content
/// - Provides excellent readability and accessibility
///
/// The system provides:
/// - 📱 Responsive text scaling
/// - 🎨 Theme-aware typography
/// - 🔤 Clear typographic hierarchy
/// - 🌐 Optimized Chinese character rendering
/// - ♿ Accessibility support
class AppTypography {
  // Private constructor to prevent instantiation
  AppTypography._();

  // =====================================================
  // FONT FAMILIES
  // =====================================================

  /// Modern sans-serif font for UI elements and learning content
  static const String sansSerifFont = 'NotoSansSC';

  /// Font for reading and formal content (now using NotoSansSC for consistency)
  static const String serifFont = 'NotoSansSC';

  // =====================================================
  // FONT WEIGHTS
  // =====================================================

  /// Light weight (300) - for subtle text
  static const FontWeight light = FontWeight.w300;

  /// Regular weight (400) - for body text
  static const FontWeight regular = FontWeight.w400;

  /// Medium weight (500) - for emphasis
  static const FontWeight medium = FontWeight.w500;

  /// Semi-bold weight (600) - for headings
  static const FontWeight semiBold = FontWeight.w600;

  /// Bold weight (700) - for strong emphasis
  static const FontWeight bold = FontWeight.w700;

  // =====================================================
  // DISPLAY TYPOGRAPHY (Large headings, hero text)
  // =====================================================

  /// Display Large - App titles, hero text
  static const TextStyle displayLarge = TextStyle(
    fontFamily: sansSerifFont,
    fontWeight: bold,
    fontSize: 57.0,
    height: 1.12,
    letterSpacing: -0.25,
  );

  /// Display Medium - Section headers
  static const TextStyle displayMedium = TextStyle(
    fontFamily: sansSerifFont,
    fontWeight: bold,
    fontSize: 45.0,
    height: 1.16,
    letterSpacing: 0.0,
  );

  /// Display Small - Page titles
  static const TextStyle displaySmall = TextStyle(
    fontFamily: sansSerifFont,
    fontWeight: bold,
    fontSize: 36.0,
    height: 1.22,
    letterSpacing: 0.0,
  );

  // =====================================================
  // HEADLINE TYPOGRAPHY (Medium headings)
  // =====================================================

  /// Headline Large - Main page headings
  static const TextStyle headlineLarge = TextStyle(
    fontFamily: sansSerifFont,
    fontWeight: semiBold,
    fontSize: 32.0,
    height: 1.25,
    letterSpacing: 0.0,
  );

  /// Headline Medium - Section headings
  static const TextStyle headlineMedium = TextStyle(
    fontFamily: sansSerifFont,
    fontWeight: semiBold,
    fontSize: 28.0,
    height: 1.29,
    letterSpacing: 0.0,
  );

  /// Headline Small - Subsection headings
  static const TextStyle headlineSmall = TextStyle(
    fontFamily: sansSerifFont,
    fontWeight: semiBold,
    fontSize: 24.0,
    height: 1.33,
    letterSpacing: 0.0,
  );

  // =====================================================
  // TITLE TYPOGRAPHY (Small headings, app bar titles)
  // =====================================================

  /// Title Large - App bar titles, dialog titles
  static const TextStyle titleLarge = TextStyle(
    fontFamily: sansSerifFont,
    fontWeight: medium,
    fontSize: 22.0,
    height: 1.27,
    letterSpacing: 0.0,
  );

  /// Title Medium - Card titles, list headers
  static const TextStyle titleMedium = TextStyle(
    fontFamily: sansSerifFont,
    fontWeight: medium,
    fontSize: 16.0,
    height: 1.50,
    letterSpacing: 0.15,
  );

  /// Title Small - Small card titles, tab labels
  static const TextStyle titleSmall = TextStyle(
    fontFamily: sansSerifFont,
    fontWeight: medium,
    fontSize: 14.0,
    height: 1.43,
    letterSpacing: 0.10,
  );

  // =====================================================
  // LABEL TYPOGRAPHY (Buttons, chips, small labels)
  // =====================================================

  /// Label Large - Primary buttons
  static const TextStyle labelLarge = TextStyle(
    fontFamily: sansSerifFont,
    fontWeight: medium,
    fontSize: 14.0,
    height: 1.43,
    letterSpacing: 0.10,
  );

  /// Label Medium - Secondary buttons, chips
  static const TextStyle labelMedium = TextStyle(
    fontFamily: sansSerifFont,
    fontWeight: medium,
    fontSize: 12.0,
    height: 1.33,
    letterSpacing: 0.50,
  );

  /// Label Small - Small buttons, badges
  static const TextStyle labelSmall = TextStyle(
    fontFamily: sansSerifFont,
    fontWeight: medium,
    fontSize: 11.0,
    height: 1.45,
    letterSpacing: 0.50,
  );

  // =====================================================
  // BODY TYPOGRAPHY (Main content text)
  // =====================================================

  /// Body Large - Main content, article text
  static const TextStyle bodyLarge = TextStyle(
    fontFamily: serifFont,
    fontWeight: regular,
    fontSize: 16.0,
    height: 1.50,
    letterSpacing: 0.15,
  );

  /// Body Medium - Secondary content, descriptions
  static const TextStyle bodyMedium = TextStyle(
    fontFamily: serifFont,
    fontWeight: regular,
    fontSize: 14.0,
    height: 1.43,
    letterSpacing: 0.25,
  );

  /// Body Small - Captions, fine print
  static const TextStyle bodySmall = TextStyle(
    fontFamily: serifFont,
    fontWeight: regular,
    fontSize: 12.0,
    height: 1.33,
    letterSpacing: 0.40,
  );

  // =====================================================
  // CHINESE CHARACTER TYPOGRAPHY (Learning content)
  // =====================================================

  /// Chinese Character Extra Large - Hero characters
  static const TextStyle chineseCharacterXL = TextStyle(
    fontFamily: sansSerifFont,
    fontWeight: bold,
    fontSize: 72.0,
    height: 1.0,
    letterSpacing: 0.0,
  );

  /// Chinese Character Large - Main display characters
  static const TextStyle chineseCharacterLarge = TextStyle(
    fontFamily: sansSerifFont,
    fontWeight: bold,
    fontSize: 64.0,
    height: 1.0,
    letterSpacing: 0.0,
  );

  /// Chinese Character Medium - Button characters, smaller displays
  static const TextStyle chineseCharacterMedium = TextStyle(
    fontFamily: sansSerifFont,
    fontWeight: bold,
    fontSize: 30.0,
    height: 1.2,
    letterSpacing: 0.0,
  );

  /// Chinese Character Small - Compact displays, lists
  static const TextStyle chineseCharacterSmall = TextStyle(
    fontFamily: sansSerifFont,
    fontWeight: semiBold,
    fontSize: 24.0,
    height: 1.25,
    letterSpacing: 0.0,
  );

  // =====================================================
  // PINYIN TYPOGRAPHY (Pronunciation guides)
  // =====================================================

  /// Pinyin Large - Main pronunciation display
  static const TextStyle pinyinLarge = TextStyle(
    fontFamily: sansSerifFont,
    fontWeight: medium,
    fontSize: 26.0,
    height: 1.23,
    letterSpacing: 0.0,
  );

  /// Pinyin Medium - Secondary pronunciation
  static const TextStyle pinyinMedium = TextStyle(
    fontFamily: sansSerifFont,
    fontWeight: regular,
    fontSize: 18.0,
    height: 1.33,
    letterSpacing: 0.15,
  );

  /// Pinyin Small - Compact pronunciation guides
  static const TextStyle pinyinSmall = TextStyle(
    fontFamily: sansSerifFont,
    fontWeight: regular,
    fontSize: 14.0,
    height: 1.43,
    letterSpacing: 0.25,
  );

  // =====================================================
  // READING TYPOGRAPHY (E-book content)
  // =====================================================

  /// Reading Title - Chapter titles, book titles
  static const TextStyle readingTitle = TextStyle(
    fontFamily: serifFont,
    fontWeight: semiBold,
    fontSize: 24.0,
    height: 1.33,
    letterSpacing: 0.0,
  );

  /// Reading Heading - Section headings in books
  static const TextStyle readingHeading = TextStyle(
    fontFamily: serifFont,
    fontWeight: medium,
    fontSize: 20.0,
    height: 1.40,
    letterSpacing: 0.0,
  );

  /// Reading Body - Main reading content
  static const TextStyle readingBody = TextStyle(
    fontFamily: serifFont,
    fontWeight: regular,
    fontSize: 16.0,
    height: 1.60,
    letterSpacing: 0.15,
  );

  /// Reading Caption - Footnotes, image captions
  static const TextStyle readingCaption = TextStyle(
    fontFamily: serifFont,
    fontWeight: regular,
    fontSize: 13.0,
    height: 1.46,
    letterSpacing: 0.40,
  );

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  /// Creates a responsive text style based on screen size and user preferences
  static TextStyle responsive(
    BuildContext context,
    TextStyle baseStyle, {
    double? minFontSize,
    double? maxFontSize,
  }) {
    final textScaler = MediaQuery.textScalerOf(context);
    final baseFontSize = baseStyle.fontSize ?? 14.0;

    // Calculate scaled font size
    final scaledFontSize = textScaler.scale(baseFontSize);

    // Apply constraints if provided
    final constrainedFontSize = scaledFontSize.clamp(
      minFontSize ?? baseFontSize * 0.8,
      maxFontSize ?? baseFontSize * 1.5,
    );

    return baseStyle.copyWith(fontSize: constrainedFontSize);
  }

  /// Creates a text style with theme-aware colors
  static TextStyle withThemeColor(
    BuildContext context,
    TextStyle baseStyle, {
    Color? lightColor,
    Color? darkColor,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final colorScheme = Theme.of(context).colorScheme;

    Color textColor;
    if (lightColor != null && darkColor != null) {
      textColor = isDark ? darkColor : lightColor;
    } else {
      textColor = colorScheme.onSurface;
    }

    return baseStyle.copyWith(color: textColor);
  }

  /// Creates a text style optimized for Chinese characters
  static TextStyle chineseOptimized(TextStyle baseStyle) {
    return baseStyle.copyWith(
      fontFamily: sansSerifFont,
      height: baseStyle.height ?? 1.2,
      letterSpacing: 0.0,
    );
  }

  /// Creates a text style optimized for reading content
  static TextStyle readingOptimized(TextStyle baseStyle) {
    return baseStyle.copyWith(
      fontFamily: serifFont,
      height: baseStyle.height ?? 1.6,
      letterSpacing: baseStyle.letterSpacing ?? 0.15,
    );
  }

  // =====================================================
  // SPECIALIZED TEXT STYLES
  // =====================================================

  /// Error text style
  static TextStyle error(BuildContext context) {
    return labelMedium.copyWith(
      color: Theme.of(context).colorScheme.error,
      fontWeight: medium,
    );
  }

  /// Success text style
  static TextStyle success(BuildContext context) {
    return labelMedium.copyWith(
      color: Theme.of(context).colorScheme.primary,
      fontWeight: medium,
    );
  }

  /// Warning text style
  static TextStyle warning(BuildContext context) {
    return labelMedium.copyWith(
      color: Theme.of(context).colorScheme.error,
      fontWeight: medium,
    );
  }

  /// Disabled text style
  static TextStyle disabled(BuildContext context) {
    return bodyMedium.copyWith(
      color: Theme.of(context).disabledColor,
    );
  }

  /// Link text style
  static TextStyle link(BuildContext context) {
    return bodyMedium.copyWith(
      color: Theme.of(context).colorScheme.primary,
      decoration: TextDecoration.underline,
    );
  }

  /// Code text style (monospace)
  static const TextStyle code = TextStyle(
    fontFamily: 'monospace',
    fontWeight: regular,
    fontSize: 14.0,
    height: 1.4,
    letterSpacing: 0.0,
  );

  // =====================================================
  // THEME INTEGRATION
  // =====================================================

  /// Gets the complete typography theme for Material 3
  static TextTheme getTextTheme(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return TextTheme(
      // Display styles
      displayLarge: displayLarge.copyWith(color: colorScheme.onSurface),
      displayMedium: displayMedium.copyWith(color: colorScheme.onSurface),
      displaySmall: displaySmall.copyWith(color: colorScheme.onSurface),

      // Headline styles
      headlineLarge: headlineLarge.copyWith(color: colorScheme.onSurface),
      headlineMedium: headlineMedium.copyWith(color: colorScheme.onSurface),
      headlineSmall: headlineSmall.copyWith(color: colorScheme.onSurface),

      // Title styles
      titleLarge: titleLarge.copyWith(color: colorScheme.onSurface),
      titleMedium: titleMedium.copyWith(color: colorScheme.onSurface),
      titleSmall: titleSmall.copyWith(color: colorScheme.onSurface),

      // Label styles
      labelLarge: labelLarge.copyWith(color: colorScheme.onSurface),
      labelMedium: labelMedium.copyWith(color: colorScheme.onSurface),
      labelSmall: labelSmall.copyWith(color: colorScheme.onSurface),

      // Body styles
      bodyLarge: bodyLarge.copyWith(color: colorScheme.onSurface),
      bodyMedium: bodyMedium.copyWith(color: colorScheme.onSurface),
      bodySmall: bodySmall.copyWith(color: colorScheme.onSurfaceVariant),
    );
  }

  /// Gets typography styles optimized for HSK learning
  static HSKTypographyStyles getHSKStyles(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return HSKTypographyStyles(
      appTitle: displayMedium.copyWith(
        color: colorScheme.onPrimary,
        shadows: [
          Shadow(
            offset: const Offset(1, 1),
            blurRadius: DesignSystem.elevationS + 1.0,
            color: DesignSystem.getStateLayerColor(colorScheme.shadow, 0.6),
          ),
        ],
      ),
      screenTitle: titleLarge.copyWith(color: colorScheme.onPrimary),
      chineseCharacter:
          chineseCharacterLarge.copyWith(color: colorScheme.onPrimary),
      pinyin: pinyinLarge.copyWith(color: colorScheme.onPrimary),
      english: bodyLarge.copyWith(
        color: DesignSystem.getStateLayerColor(colorScheme.onPrimary, 0.7),
      ),
      button: labelLarge.copyWith(color: colorScheme.onPrimary),
      feedback: headlineSmall.copyWith(color: colorScheme.onPrimary),
    );
  }

  /// Gets typography styles optimized for reading
  static ReadingTypographyStyles getReadingStyles(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return ReadingTypographyStyles(
      title: readingTitle.copyWith(color: colorScheme.onSurface),
      heading: readingHeading.copyWith(color: colorScheme.onSurface),
      body: readingBody.copyWith(color: colorScheme.onSurface),
      caption: readingCaption.copyWith(color: colorScheme.onSurfaceVariant),
    );
  }
}

// =====================================================
// SPECIALIZED TYPOGRAPHY STYLE CLASSES
// =====================================================

/// Typography styles for HSK learning components
class HSKTypographyStyles {
  final TextStyle appTitle;
  final TextStyle screenTitle;
  final TextStyle chineseCharacter;
  final TextStyle pinyin;
  final TextStyle english;
  final TextStyle button;
  final TextStyle feedback;

  const HSKTypographyStyles({
    required this.appTitle,
    required this.screenTitle,
    required this.chineseCharacter,
    required this.pinyin,
    required this.english,
    required this.button,
    required this.feedback,
  });
}

/// Typography styles for reading components
class ReadingTypographyStyles {
  final TextStyle title;
  final TextStyle heading;
  final TextStyle body;
  final TextStyle caption;

  const ReadingTypographyStyles({
    required this.title,
    required this.heading,
    required this.body,
    required this.caption,
  });
}
