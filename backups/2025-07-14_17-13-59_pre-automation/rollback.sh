#!/bin/bash
# DassoShu Reader - Rollback Design System Automation
# Generated: 2025-07-14 17:14:53.258323

cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-13-59_pre-automation/lib/page/reading_page.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/page/reading_page.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-13-59_pre-automation/lib/page/home_page/hsk_page/hsk_home_screen.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/page/home_page/hsk_page/hsk_home_screen.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-13-59_pre-automation/lib/page/home_page/hsk_page/hsk_learn_screen.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/page/home_page/hsk_page/hsk_learn_screen.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-13-59_pre-automation/lib/page/home_page/hsk_page/hsk_review_screen.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/page/home_page/hsk_page/hsk_review_screen.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-13-59_pre-automation/lib/page/settings_page/narrate.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/page/settings_page/narrate.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-13-59_pre-automation/lib/utils/state_management/lazy_loading_widgets.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/utils/state_management/lazy_loading_widgets.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-13-59_pre-automation/lib/utils/state_management/state_flow_debugger.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/utils/state_management/state_flow_debugger.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-13-59_pre-automation/lib/utils/state_management/const_audit.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/utils/state_management/const_audit.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-13-59_pre-automation/lib/utils/accessibility/focus_management.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/utils/accessibility/focus_management.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-13-59_pre-automation/lib/widgets/settings/user_profile_section.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/widgets/settings/user_profile_section.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-13-59_pre-automation/lib/widgets/common/orientation_aware_widgets.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/widgets/common/orientation_aware_widgets.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-13-59_pre-automation/lib/widgets/dictionary/context_menu_character_tab.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/widgets/dictionary/context_menu_character_tab.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-13-59_pre-automation/lib/widgets/dictionary/accessible_dictionary_tab.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/widgets/dictionary/accessible_dictionary_tab.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-13-59_pre-automation/lib/widgets/reading_page/text_selection_mode_toggle.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/widgets/reading_page/text_selection_mode_toggle.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-13-59_pre-automation/lib/widgets/reading_page/search_page.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/widgets/reading_page/search_page.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-13-59_pre-automation/lib/widgets/context_menu/context_menu_tabbed_content.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/widgets/context_menu/context_menu_tabbed_content.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_17-13-59_pre-automation/lib/widgets/debug/manufacturer_detection_debug_widget.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/widgets/debug/manufacturer_detection_debug_widget.dart'

echo 'Rollback completed successfully!'
