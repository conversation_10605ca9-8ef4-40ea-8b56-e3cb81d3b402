import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/semantics.dart';

/// Focus order priority levels
enum FocusPriority {
  critical, // Navigation, primary actions
  high, // Secondary actions, important content
  medium, // Content, form fields
  low, // Supplementary content
  decorative, // Should not receive focus
}

/// Focus management and keyboard navigation system for accessibility
///
/// This system provides comprehensive focus management including:
/// - Logical focus order and navigation
/// - Keyboard shortcuts and navigation
/// - Focus indicators and visual feedback
/// - Screen reader focus coordination
class FocusManagement {
  // Private constructor to prevent instantiation
  FocusManagement._();

  // =====================================================
  // FOCUS ORDER MANAGEMENT
  // =====================================================

  /// Create focus node with priority
  static FocusNode createFocusNode({
    String? debugLabel,
    FocusPriority priority = FocusPriority.medium,
    bool canRequestFocus = true,
    bool skipTraversal = false,
  }) {
    return FocusNode(
      debugLabel: debugLabel,
      canRequestFocus: canRequestFocus && priority != FocusPriority.decorative,
      skipTraversal: skipTraversal || priority == FocusPriority.decorative,
    );
  }

  /// Create focus scope for logical grouping
  static FocusScopeNode createFocusScope({
    String? debugLabel,
    bool canRequestFocus = true,
  }) {
    return FocusScopeNode(
      debugLabel: debugLabel,
      canRequestFocus: canRequestFocus,
    );
  }

  // =====================================================
  // KEYBOARD NAVIGATION
  // =====================================================

  /// Standard keyboard shortcuts for navigation
  static Map<LogicalKeySet, Intent> get standardShortcuts => {
        // Tab navigation
        LogicalKeySet(LogicalKeyboardKey.tab): const NextFocusIntent(),
        LogicalKeySet(LogicalKeyboardKey.shift, LogicalKeyboardKey.tab):
            const PreviousFocusIntent(),

        // Arrow navigation
        LogicalKeySet(LogicalKeyboardKey.arrowDown):
            const DirectionalFocusIntent(TraversalDirection.down),
        LogicalKeySet(LogicalKeyboardKey.arrowUp):
            const DirectionalFocusIntent(TraversalDirection.up),
        LogicalKeySet(LogicalKeyboardKey.arrowLeft):
            const DirectionalFocusIntent(TraversalDirection.left),
        LogicalKeySet(LogicalKeyboardKey.arrowRight):
            const DirectionalFocusIntent(TraversalDirection.right),

        // Home/End navigation
        LogicalKeySet(LogicalKeyboardKey.home): const ScrollToTopIntent(),
        LogicalKeySet(LogicalKeyboardKey.end): const ScrollToBottomIntent(),

        // Page navigation
        LogicalKeySet(LogicalKeyboardKey.pageUp): const PageUpIntent(),
        LogicalKeySet(LogicalKeyboardKey.pageDown): const PageDownIntent(),

        // Escape
        LogicalKeySet(LogicalKeyboardKey.escape): const DismissIntent(),
      };

  /// Create keyboard navigation wrapper
  static Widget createKeyboardNavigationWrapper({
    required Widget child,
    Map<LogicalKeySet, Intent>? additionalShortcuts,
    Map<Type, Action<Intent>>? additionalActions,
  }) {
    final shortcuts = <LogicalKeySet, Intent>{
      ...standardShortcuts,
      if (additionalShortcuts != null) ...additionalShortcuts,
    };

    final actions = <Type, Action<Intent>>{
      ScrollToTopIntent: ScrollToTopAction(),
      ScrollToBottomIntent: ScrollToBottomAction(),
      PageUpIntent: PageUpAction(),
      PageDownIntent: PageDownAction(),
      if (additionalActions != null) ...additionalActions,
    };

    return Shortcuts(
      shortcuts: shortcuts,
      child: Actions(
        actions: actions,
        child: child,
      ),
    );
  }

  // =====================================================
  // FOCUS INDICATORS
  // =====================================================

  /// Create accessible focus indicator
  static Widget createFocusIndicator({
    required Widget child,
    required FocusNode focusNode,
    Color? focusColor,
    double borderWidth = 2.0,
    BorderRadius? borderRadius,
  }) {
    return Focus(
      focusNode: focusNode,
      child: Builder(
        builder: (context) {
          final theme = Theme.of(context);
          final effectiveFocusColor = focusColor ?? theme.colorScheme.primary;

          return AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            decoration: BoxDecoration(
              border: focusNode.hasFocus
                  ? Border.all(
                      color: effectiveFocusColor,
                      width: borderWidth,
                    )
                  : null,
              borderRadius: borderRadius ?? BorderRadius.circular(4.0),
            ),
            child: child,
          );
        },
      ),
    );
  }

  /// Create focus-aware button
  static Widget createFocusableButton({
    required Widget child,
    required VoidCallback? onPressed,
    FocusNode? focusNode,
    String? semanticLabel,
    String? tooltip,
    Color? focusColor,
  }) {
    final effectiveFocusNode = focusNode ?? FocusNode();

    return createFocusIndicator(
      focusNode: effectiveFocusNode,
      focusColor: focusColor,
      child: Semantics(
        label: semanticLabel,
        button: true,
        enabled: onPressed != null,
        child: Tooltip(
          message: tooltip ?? semanticLabel ?? '',
          child: InkWell(
            focusNode: effectiveFocusNode,
            onTap: onPressed,
            child: child,
          ),
        ),
      ),
    );
  }

  // =====================================================
  // FOCUS TRAVERSAL
  // =====================================================

  /// Create ordered focus traversal policy
  static FocusTraversalPolicy createOrderedTraversalPolicy({
    List<FocusNode>? order,
  }) {
    // OrderedTraversalPolicy doesn't have an order parameter in Flutter
    // Use ReadingOrderTraversalPolicy instead
    return ReadingOrderTraversalPolicy();
  }

  /// Create focus traversal group
  static Widget createFocusTraversalGroup({
    required Widget child,
    FocusTraversalPolicy? policy,
    bool descendantsAreFocusable = true,
  }) {
    return FocusTraversalGroup(
      policy: policy ?? ReadingOrderTraversalPolicy(),
      descendantsAreFocusable: descendantsAreFocusable,
      child: child,
    );
  }

  // =====================================================
  // ACCESSIBILITY FOCUS HELPERS
  // =====================================================

  /// Request focus with accessibility announcement
  static void requestFocusWithAnnouncement({
    required BuildContext context,
    required FocusNode focusNode,
    String? announcement,
  }) {
    focusNode.requestFocus();

    if (announcement != null) {
      SemanticsService.announce(
        announcement,
        TextDirection.ltr,
      );
    }
  }

  /// Move focus to next focusable element
  static void focusNext(BuildContext context) {
    FocusScope.of(context).nextFocus();
  }

  /// Move focus to previous focusable element
  static void focusPrevious(BuildContext context) {
    FocusScope.of(context).previousFocus();
  }

  /// Clear focus
  static void clearFocus(BuildContext context) {
    FocusScope.of(context).unfocus();
  }

  // =====================================================
  // SCREEN READER COORDINATION
  // =====================================================

  /// Announce content change to screen readers
  static void announceToScreenReader({
    required String message,
    TextDirection textDirection = TextDirection.ltr,
  }) {
    SemanticsService.announce(message, textDirection);
  }

  /// Announce page change
  static void announcePageChange({
    required String pageName,
    TextDirection textDirection = TextDirection.ltr,
  }) {
    SemanticsService.announce(
      'Navigated to $pageName',
      textDirection,
    );
  }

  /// Announce content update
  static void announceContentUpdate({
    required String updateDescription,
    TextDirection textDirection = TextDirection.ltr,
  }) {
    SemanticsService.announce(
      updateDescription,
      textDirection,
    );
  }
}

// =====================================================
// CUSTOM INTENTS AND ACTIONS
// =====================================================

/// Intent for scrolling to top
class ScrollToTopIntent extends Intent {
  const ScrollToTopIntent();
}

/// Intent for scrolling to bottom
class ScrollToBottomIntent extends Intent {
  const ScrollToBottomIntent();
}

/// Intent for page up
class PageUpIntent extends Intent {
  const PageUpIntent();
}

/// Intent for page down
class PageDownIntent extends Intent {
  const PageDownIntent();
}

/// Action for scrolling to top
class ScrollToTopAction extends Action<ScrollToTopIntent> {
  @override
  Object? invoke(ScrollToTopIntent intent) {
    // Implementation would depend on the specific scrollable widget
    return null;
  }
}

/// Action for scrolling to bottom
class ScrollToBottomAction extends Action<ScrollToBottomIntent> {
  @override
  Object? invoke(ScrollToBottomIntent intent) {
    // Implementation would depend on the specific scrollable widget
    return null;
  }
}

/// Action for page up
class PageUpAction extends Action<PageUpIntent> {
  @override
  Object? invoke(PageUpIntent intent) {
    // Implementation would depend on the specific scrollable widget
    return null;
  }
}

/// Action for page down
class PageDownAction extends Action<PageDownIntent> {
  @override
  Object? invoke(PageDownIntent intent) {
    // Implementation would depend on the specific scrollable widget
    return null;
  }
}

/// Mixin for widgets that need focus management
mixin FocusManagementMixin<T extends StatefulWidget> on State<T> {
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusManagement.createFocusNode(
      debugLabel: widget.runtimeType.toString(),
    );
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  /// Get the focus node for this widget
  FocusNode get focusNode => _focusNode;

  /// Request focus for this widget
  void requestFocus() {
    _focusNode.requestFocus();
  }

  /// Request focus with screen reader announcement
  void requestFocusWithAnnouncement(String announcement) {
    FocusManagement.requestFocusWithAnnouncement(
      context: context,
      focusNode: _focusNode,
      announcement: announcement,
    );
  }
}
