import 'package:flutter/material.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/enums/text_selection_mode.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';

/// A toggle widget for switching between text selection modes
class TextSelectionModeToggle extends StatefulWidget {
  /// Callback when selection mode changes
  final void Function(TextSelectionMode mode) onModeChanged;

  /// Text color for the toggle
  final Color? textColor;

  /// Background color for the toggle
  final Color? backgroundColor;

  const TextSelectionModeToggle({
    super.key,
    required this.onModeChanged,
    this.textColor,
    this.backgroundColor,
  });

  @override
  State<TextSelectionModeToggle> createState() =>
      _TextSelectionModeToggleState();
}

class _TextSelectionModeToggleState extends State<TextSelectionModeToggle> {
  late TextSelectionMode _currentMode;

  @override
  void initState() {
    super.initState();
    _currentMode = Prefs().textSelectionMode;
  }

  void _toggleMode() {
    setState(() {
      _currentMode = _currentMode == TextSelectionMode.free
          ? TextSelectionMode.segmentation
          : TextSelectionMode.free;
    });

    // Save to preferences
    Prefs().textSelectionMode = _currentMode;

    // Notify parent
    widget.onModeChanged(_currentMode);
  }

  @override
  Widget build(BuildContext context) {
    final textColor =
        widget.textColor ?? Theme.of(context).colorScheme.onSurface;
    final isSegmentationMode = _currentMode == TextSelectionMode.segmentation;

    return SemanticHelpers.button(
      context: context,
      label: isSegmentationMode
          ? L10n.of(context).reading_page_segmentation_mode
          : L10n.of(context).reading_page_free_selection_mode,
      hint: isSegmentationMode
          ? L10n.of(context).reading_page_switch_to_free_selection
          : L10n.of(context).reading_page_switch_to_segmentation,
      onTap: _toggleMode,
      child: ConstrainedBox(
        constraints: DesignSystem.getMinTouchTargetConstraints(),
        child: IconButton(
          onPressed: _toggleMode,
          tooltip: isSegmentationMode
              ? L10n.of(context).reading_page_switch_to_free_selection
              : L10n.of(context).reading_page_switch_to_segmentation,
          icon: AnimatedSwitcher(
            duration: const Duration(milliseconds: 200),
            child: Icon(
              isSegmentationMode
                  ? Icons.auto_fix_high_outlined // Segmentation mode icon
                  : Icons.text_fields_outlined, // Free selection mode icon
              key: ValueKey(isSegmentationMode),
              color: textColor,
              size: 24,
            ),
          ),
        ),
      ),
    );
  }
}

/// A compact version of the text selection mode toggle for use in tight spaces
class CompactTextSelectionModeToggle extends StatefulWidget {
  /// Callback when selection mode changes
  final void Function(TextSelectionMode mode) onModeChanged;

  /// Text color for the toggle
  final Color? textColor;

  /// Background color for the toggle
  final Color? backgroundColor;

  const CompactTextSelectionModeToggle({
    super.key,
    required this.onModeChanged,
    this.textColor,
    this.backgroundColor,
  });

  @override
  State<CompactTextSelectionModeToggle> createState() =>
      _CompactTextSelectionModeToggleState();
}

class _CompactTextSelectionModeToggleState
    extends State<CompactTextSelectionModeToggle> {
  late TextSelectionMode _currentMode;

  @override
  void initState() {
    super.initState();
    _currentMode = Prefs().textSelectionMode;
  }

  void _toggleMode() {
    setState(() {
      _currentMode = _currentMode == TextSelectionMode.free
          ? TextSelectionMode.segmentation
          : TextSelectionMode.free;
    });

    // Save to preferences
    Prefs().textSelectionMode = _currentMode;

    // Notify parent
    widget.onModeChanged(_currentMode);
  }

  @override
  Widget build(BuildContext context) {
    final textColor =
        widget.textColor ?? Theme.of(context).colorScheme.onSurface;
    final isSegmentationMode = _currentMode == TextSelectionMode.segmentation;

    return SemanticHelpers.button(
      context: context,
      label: isSegmentationMode
          ? L10n.of(context).reading_page_segmentation_mode
          : L10n.of(context).reading_page_free_selection_mode,
      hint: isSegmentationMode
          ? L10n.of(context).reading_page_switch_to_free_selection
          : L10n.of(context).reading_page_switch_to_segmentation,
      onTap: _toggleMode,
      child: ConstrainedBox(
        constraints: DesignSystem.getMinTouchTargetConstraints(),
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: DesignSystem.spaceS,
            vertical: DesignSystem.spaceXS,
          ),
          decoration: BoxDecoration(
            color: isSegmentationMode
                ? Theme.of(context)
                    .colorScheme
                    .primaryContainer
                    .withValues(alpha: 0.3)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(DesignSystem.radiusML),
            border: Border.all(
              color: textColor.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 200),
                child: Icon(
                  isSegmentationMode
                      ? AdaptiveIcons.autoFixHighOutlined
                      : AdaptiveIcons.textFieldsOutlined,
                  key: ValueKey(isSegmentationMode),
                  color: textColor,
                  size: DesignSystem.getAdjustedIconSize(16),
                ),
              ),
              const SizedBox(width: DesignSystem.spaceXS),
              Text(
                isSegmentationMode
                    ? '词'
                    : '自', // Chinese characters for word/free
                style: TextStyle(
                  color: textColor,
                  fontSize: DesignSystem.fontSizeS,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
