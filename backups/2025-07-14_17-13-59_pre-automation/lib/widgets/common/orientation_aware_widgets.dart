import 'package:flutter/material.dart';
import '../../config/responsive_system.dart';
import '../../config/design_system.dart';
import '../../utils/orientation_manager.dart';

/// A widget that adapts its layout based on device orientation
class OrientationAwareLayout extends StatelessWidget {
  const OrientationAwareLayout({
    super.key,
    required this.portraitBuilder,
    required this.landscapeBuilder,
    this.transitionDuration = const Duration(milliseconds: 300),
    this.preserveState = true,
  });

  final Widget Function(BuildContext context) portraitBuilder;
  final Widget Function(BuildContext context) landscapeBuilder;
  final Duration transitionDuration;
  final bool preserveState;

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(
      builder: (context, orientation) {
        final child = orientation == Orientation.portrait
            ? portraitBuilder(context)
            : landscapeBuilder(context);

        if (preserveState) {
          return OrientationManager.createOrientationTransition(
            duration: transitionDuration,
            child: child,
          );
        }

        return child;
      },
    );
  }
}

/// A container that adapts its padding based on orientation
class OrientationAwarePadding extends StatelessWidget {
  const OrientationAwarePadding({
    super.key,
    required this.child,
    this.portraitPadding,
    this.landscapePadding,
    this.useReadingPadding = false,
  });

  final Widget child;
  final EdgeInsetsGeometry? portraitPadding;
  final EdgeInsetsGeometry? landscapePadding;
  final bool useReadingPadding;

  @override
  Widget build(BuildContext context) {
    final isLandscape = ResponsiveSystem.isLandscape(context);

    EdgeInsetsGeometry padding;

    if (useReadingPadding) {
      padding = ResponsiveSystem.getOrientationAwareReadingPadding(context);
    } else {
      padding = isLandscape
          ? (landscapePadding ?? DesignSystem.pagePadding)
          : (portraitPadding ?? DesignSystem.pagePadding);
    }

    return Padding(
      padding: padding,
      child: child,
    );
  }
}

/// A widget that switches between Row and Column based on orientation
class OrientationAwareRowColumn extends StatelessWidget {
  const OrientationAwareRowColumn({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
    this.forceVertical = false,
    this.minWidthForHorizontal = 600,
  });

  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;
  final bool forceVertical;
  final double minWidthForHorizontal;

  @override
  Widget build(BuildContext context) {
    final shouldUseHorizontal = !forceVertical &&
        OrientationManager.shouldUseHorizontalLayout(
          context,
          minWidth: minWidthForHorizontal.toInt(),
        );

    if (shouldUseHorizontal) {
      return Row(
        mainAxisAlignment: mainAxisAlignment,
        crossAxisAlignment: crossAxisAlignment,
        mainAxisSize: mainAxisSize,
        children: children,
      );
    } else {
      return Column(
        mainAxisAlignment: mainAxisAlignment,
        crossAxisAlignment: crossAxisAlignment,
        mainAxisSize: mainAxisSize,
        children: children,
      );
    }
  }
}

/// A dialog that adapts its size and position based on orientation
class OrientationAwareDialog extends StatelessWidget {
  const OrientationAwareDialog({
    super.key,
    required this.child,
    this.title,
    this.actions,
    this.scrollable = true,
  });

  final Widget child;
  final Widget? title;
  final List<Widget>? actions;
  final bool scrollable;

  @override
  Widget build(BuildContext context) {
    final constraints =
        ResponsiveSystem.getOrientationAwareDialogConstraints(context);

    return ConstrainedBox(
      constraints: constraints,
      child: AlertDialog(
        title: title,
        content: scrollable ? SingleChildScrollView(child: child) : child,
        actions: actions,
        scrollable: false, // We handle scrolling ourselves
      ),
    );
  }

  /// Shows the orientation-aware dialog
  static Future<T?> show<T>({
    required BuildContext context,
    required Widget child,
    Widget? title,
    List<Widget>? actions,
    bool scrollable = true,
    bool barrierDismissible = true,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => OrientationAwareDialog(
        title: title,
        actions: actions,
        scrollable: scrollable,
        child: child,
      ),
    );
  }
}

/// A bottom sheet that adapts its height based on orientation
class OrientationAwareBottomSheet extends StatelessWidget {
  const OrientationAwareBottomSheet({
    super.key,
    required this.child,
    this.title,
    this.scrollable = true,
  });

  final Widget child;
  final Widget? title;
  final bool scrollable;

  @override
  Widget build(BuildContext context) {
    final constraints =
        ResponsiveSystem.getOrientationAwareBottomSheetConstraints(context);

    return ConstrainedBox(
      constraints: constraints,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (title != null) ...[
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: title!,
            ),
            const Divider(height: 1),
          ],
          if (scrollable)
            Flexible(
              child: SingleChildScrollView(
                child: child,
              ),
            )
          else
            child,
        ],
      ),
    );
  }

  /// Shows the orientation-aware bottom sheet
  static Future<T?> show<T>({
    required BuildContext context,
    required Widget child,
    Widget? title,
    bool scrollable = true,
    bool isDismissible = true,
    bool enableDrag = true,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      isScrollControlled: true,
      builder: (context) => OrientationAwareBottomSheet(
        title: title,
        scrollable: scrollable,
        child: child,
      ),
    );
  }
}

/// A widget that preserves its state during orientation changes
class StatePreservingWidget extends StatefulWidget {
  const StatePreservingWidget({
    super.key,
    required this.child,
    required this.storageKey,
  });

  final Widget child;
  final String storageKey;

  @override
  State<StatePreservingWidget> createState() => _StatePreservingWidgetState();
}

class _StatePreservingWidgetState extends State<StatePreservingWidget>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return PageStorage(
      bucket: PageStorageBucket(),
      child: KeyedSubtree(
        key: OrientationManager.createStorageKey(widget.storageKey),
        child: widget.child,
      ),
    );
  }
}

/// A form that preserves input state during orientation changes
class OrientationAwareForm extends StatefulWidget {
  const OrientationAwareForm({
    super.key,
    required this.child,
    this.onOrientationChanged,
  });

  final Widget child;
  final VoidCallback? onOrientationChanged;

  @override
  State<OrientationAwareForm> createState() => _OrientationAwareFormState();
}

class _OrientationAwareFormState extends State<OrientationAwareForm> {
  Orientation? _lastOrientation;
  final List<TextEditingController> _controllers = [];
  Map<String, dynamic>? _savedState;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    final currentOrientation = MediaQuery.of(context).orientation;

    if (_lastOrientation != null && _lastOrientation != currentOrientation) {
      // Orientation changed - preserve and restore state
      _savedState = OrientationManager.preserveFormState(_controllers);

      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_savedState != null) {
          OrientationManager.restoreFormState(_controllers, _savedState!);
        }
        widget.onOrientationChanged?.call();
      });
    }

    _lastOrientation = currentOrientation;
  }

  void registerController(TextEditingController controller) {
    if (!_controllers.contains(controller)) {
      _controllers.add(controller);
    }
  }

  void unregisterController(TextEditingController controller) {
    _controllers.remove(controller);
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }

  @override
  void dispose() {
    _controllers.clear();
    super.dispose();
  }
}
