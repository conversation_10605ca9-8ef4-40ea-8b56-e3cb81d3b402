import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/widgets/dictionary/context_menu_dictionary_tab.dart';
import 'package:dasso_reader/widgets/dictionary/context_menu_word_segmentation_tab.dart';
import 'package:dasso_reader/widgets/dictionary/context_menu_character_tab.dart';
import 'package:dasso_reader/service/dictionary/chinese_segmentation_service.dart';
import 'package:flutter/material.dart';
import 'package:dasso_reader/config/design_system.dart';

/// A tabbed content widget for the context menu that shows Dictionary and Word Segmentation tabs
class ContextMenuTabbedContent extends StatefulWidget {
  final String selectedText;
  final int bookId;

  const ContextMenuTabbedContent({
    super.key,
    required this.selectedText,
    required this.bookId,
  });

  @override
  State<ContextMenuTabbedContent> createState() =>
      _ContextMenuTabbedContentState();
}

class _ContextMenuTabbedContentState extends State<ContextMenuTabbedContent>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isChinese = false;
  bool _showSetWordTab = false;
  bool _showCharTab = false;
  final List<Widget> _visibleTabs = [];
  final List<Widget> _visibleTabViews = [];

  @override
  void initState() {
    super.initState();
    _isChinese = RegExp(r'[\u4e00-\u9fa5]').hasMatch(widget.selectedText);

    // Determine tab visibility and initialize controller
    _initializeTabVisibility();
  }

  /// Initialize tab visibility based on content analysis
  void _initializeTabVisibility() {
    _visibleTabs.clear();
    _visibleTabViews.clear();

    // Dict tab is always visible for Chinese text
    if (_isChinese) {
      _visibleTabs.add(_buildDictTab());
      _visibleTabViews.add(_buildDictTabView());

      // Check if SET/WORD tab should be visible
      _showSetWordTab = _shouldShowSetWordTab();
      if (_showSetWordTab) {
        _visibleTabs.add(_buildSetWordTab());
        _visibleTabViews.add(_buildSetWordTabView());
      }

      // Check if CHAR tab should be visible
      _showCharTab = _shouldShowCharTab();
      if (_showCharTab) {
        _visibleTabs.add(_buildCharTab());
        _visibleTabViews.add(_buildCharTabView());
      }
    }

    // Initialize TabController with the correct number of visible tabs
    final tabCount = _isChinese ? _visibleTabs.length : 1;
    _tabController = TabController(
      length: tabCount,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Check if SET/WORD tab should be visible
  /// Returns true only if meaningful segmentation results are available
  bool _shouldShowSetWordTab() {
    // For very short single characters, segmentation is not meaningful
    if (widget.selectedText.length == 1) {
      return false;
    }

    // Check if segmentation data exists in cache
    final segmentationService = ChineseSegmentationService();
    final cachedData = segmentationService.getStoredSegmentationData(
      selectedText: widget.selectedText,
      startOffset: 0,
      endOffset: widget.selectedText.length,
      bookId: widget.bookId,
    );

    // If cached data exists, validate its quality
    if (cachedData != null) {
      return _hasValidSegmentationResults(cachedData);
    }

    // For texts without cached data, be very conservative
    // Only show the tab if we're confident there will be meaningful segmentation
    return _isChinese &&
        widget.selectedText.length > 2 && // Require at least 3 characters
        _isHighlyLikelyToHaveMeaningfulSegmentation();
  }

  /// Check if segmentation data contains meaningful results
  /// Returns true only if there are valid word segments with dictionary entries
  bool _hasValidSegmentationResults(Map<String, dynamic> segmentationData) {
    // Check segmentation options
    final segmentationOptions =
        segmentationData['segmentationOptions'] as List<dynamic>? ?? [];

    // Count meaningful multi-character segments
    int meaningfulSegments = 0;

    for (final option in segmentationOptions) {
      final optionMap = option as Map<String, dynamic>;
      final word = optionMap['word'] as String;

      // Look for meaningful segments that are:
      // 1. Different from the original text
      // 2. Longer than 1 character (not just character-by-character fallback)
      // 3. Not just individual characters from the original text
      if (word != widget.selectedText &&
          word.trim().isNotEmpty &&
          word.length > 1 &&
          !_isCharacterByCharacterFallback(word)) {
        meaningfulSegments++;
      }
    }

    // Check possible words from boundary detection
    final possibleWords =
        segmentationData['possibleWords'] as List<dynamic>? ?? [];

    for (final wordData in possibleWords) {
      final wordMap = wordData as Map<String, dynamic>;
      final word = wordMap['word'] as String;

      if (word != widget.selectedText &&
          word.trim().isNotEmpty &&
          word.length > 1 &&
          !_isCharacterByCharacterFallback(word)) {
        meaningfulSegments++;
      }
    }

    // Only show the tab if we have at least one meaningful multi-character segment
    return meaningfulSegments > 0;
  }

  /// Check if a word is just a character-by-character fallback
  /// This detects when segmentation failed to find meaningful compound words
  bool _isCharacterByCharacterFallback(String word) {
    // If the word is just a single character from our original text, it's a fallback
    if (word.length == 1) {
      return widget.selectedText.contains(word);
    }

    // For 2-character words, check if they're likely transliterated names
    // or foreign words that don't form meaningful Chinese compounds
    if (word.length == 2) {
      return _isLikelyTransliteratedName(word);
    }

    return false;
  }

  /// Detect likely transliterated names and foreign words
  /// These typically don't segment into meaningful Chinese compound words
  bool _isLikelyTransliteratedName(String word) {
    // Common patterns in transliterated names:
    // 1. Characters that are rarely used in native Chinese compound words
    // 2. Combinations that don't follow typical Chinese word formation patterns

    const transliterationChars = {
      // Common characters used in foreign name transliterations
      '斯', '特', '敏', '约', '翰', '玛', '丽', '汤', '姆', '克', '里',
      '卡', '拉', '娜', '莎', '安', '妮', '亚', '尔', '伯', '格', '曼', '森',
      '逊', '威', '廉', '詹', '罗', '查', '理', '戴', '维', '史', '密',
      '布', '朗', '琼', '杰', '迈', '保', '彼', '得', '马', '丁', '路', '德',
      '金', '凯', '瑟', '琳', '伊', '白', '海', '伦', '南', '希', '苏',
      '珊', '芭', '黛', '帕', '夏', '米', '歇', '吉', '西',
      '阿', '梅', '薇', '多', '利', '瓦', '莱', '奥', '昂',
      '纳', '芬', '奇',
    };

    // Check if both characters are commonly used in transliterations
    if (word.length == 2) {
      final char1 = word[0];
      final char2 = word[1];

      // If both characters are transliteration characters, likely a foreign name
      if (transliterationChars.contains(char1) &&
          transliterationChars.contains(char2)) {
        return true;
      }
    }

    // Additional pattern: check for uncommon character combinations
    // that don't follow Chinese morphological patterns
    return _hasUncommonCharacterCombination(word);
  }

  /// Check for uncommon character combinations that suggest transliteration
  bool _hasUncommonCharacterCombination(String word) {
    if (word.length != 2) return false;

    // Patterns that are uncommon in native Chinese compound words
    const uncommonCombinations = {
      '斯特',
      '斯敏',
      '约翰',
      '玛丽',
      '汤姆',
      '克里',
      '卡拉',
      '娜莎',
      '安妮',
      '亚尔',
      '伯格',
      '曼森',
      '逊威',
      '廉詹',
      '罗伯',
      '查理',
      '戴维',
      '史密',
      '布朗',
      '琼斯',
      '杰克',
      '迈克',
      '保罗',
      '彼得',
      '马丁',
      '路德',
      '凯瑟',
      '琳伊',
      '莎白',
      '格丽',
      '海伦',
      '南希',
      '苏珊',
      '芭拉',
      '黛安',
      '卡罗',
      '琳达',
      '帕特',
      '夏米',
      '歇尔',
      '吉拉',
      '西卡',
      '阿曼',
      '梅莎',
      '希瑟',
      '薇多',
      '亚瓦',
      '莱里',
      '奥莱',
      '昂纳',
      '达芬',
      '芬奇',
    };

    return uncommonCombinations.contains(word);
  }

  /// Highly conservative check for meaningful segmentation
  /// Only returns true for texts very likely to have compound words
  bool _isHighlyLikelyToHaveMeaningfulSegmentation() {
    // Be very conservative - only show for longer texts that are very likely
    // to contain meaningful Chinese compound words
    if (widget.selectedText.length < 4) {
      return false; // Too short to be confident about meaningful segmentation
    }

    // For longer texts, check if they contain common Chinese word patterns
    return _containsCommonChineseWords() && !_isLikelyAllTransliteration();
  }

  /// Check if the entire text is likely a transliterated phrase
  bool _isLikelyAllTransliteration() {
    // If more than 70% of characters are transliteration characters,
    // it's likely a foreign name or phrase
    int transliterationCount = 0;
    const transliterationChars = {
      '斯',
      '特',
      '敏',
      '约',
      '翰',
      '玛',
      '丽',
      '汤',
      '姆',
      '克',
      '里',
      '卡',
      '拉',
      '娜',
      '莎',
      '安',
      '妮',
      '亚',
      '尔',
      '伯',
      '格',
      '曼',
      '森',
      '逊',
      '威',
      '廉',
      '詹',
      '罗',
      '查',
      '理',
      '戴',
      '维',
      '史',
      '密',
      '布',
      '朗',
      '琼',
      '杰',
      '迈',
      '保',
      '彼',
      '得',
      '马',
      '丁',
      '路',
      '德',
      '金',
      '凯',
      '瑟',
      '琳',
      '伊',
      '白',
      '海',
      '伦',
      '南',
      '希',
      '苏',
      '珊',
      '芭',
      '黛',
      '帕',
      '夏',
      '米',
      '歇',
      '吉',
      '西',
      '阿',
      '梅',
      '薇',
      '多',
      '利',
      '瓦',
      '莱',
      '奥',
      '昂',
      '纳',
      '芬',
      '奇',
    };

    for (int i = 0; i < widget.selectedText.length; i++) {
      if (transliterationChars.contains(widget.selectedText[i])) {
        transliterationCount++;
      }
    }

    return transliterationCount / widget.selectedText.length > 0.7;
  }

  /// Check if the text contains common Chinese characters that form words
  /// vs transliterated names or foreign words
  bool _containsCommonChineseWords() {
    // Common Chinese characters that frequently appear in meaningful words
    const commonWordChars = {
      '的',
      '了',
      '在',
      '是',
      '我',
      '有',
      '他',
      '这',
      '个',
      '们',
      '来',
      '到',
      '时',
      '大',
      '地',
      '为',
      '子',
      '中',
      '你',
      '说',
      '生',
      '国',
      '年',
      '着',
      '就',
      '那',
      '和',
      '要',
      '她',
      '出',
      '也',
      '得',
      '里',
      '后',
      '自',
      '以',
      '会',
      '家',
      '可',
      '下',
      '而',
      '过',
      '天',
      '去',
      '能',
      '对',
      '小',
      '多',
      '然',
      '于',
      '心',
      '学',
      '么',
      '之',
      '都',
      '好',
      '看',
      '起',
      '发',
      '当',
      '没',
      '成',
      '只',
      '如',
      '事',
      '把',
      '还',
      '用',
      '第',
      '样',
      '道',
      '想',
      '作',
      '种',
      '开',
      '美',
      '总',
      '从',
      '无',
      '情',
      '己',
      '面',
      '最',
      '女',
      '但',
      '现',
      '前',
      '些',
      '所',
      '同',
      '日',
      '手',
      '又',
      '行',
      '意',
      '动',
      '方',
      '期',
      '它',
      '头',
      '经',
      '长',
      '儿',
      '回',
      '位',
      '分',
      '爱',
      '老',
      '因',
      '很',
      '给',
      '名',
      '法',
      '间',
      '斯',
      '知',
      '世',
      '什',
      '两',
      '次',
      '使',
      '身',
      '者',
      '被',
      '高',
      '已',
      '亲',
      '其',
      '进',
      '此',
    };

    // Check if any character in the text is a common word character
    for (int i = 0; i < widget.selectedText.length; i++) {
      if (commonWordChars.contains(widget.selectedText[i])) {
        return true;
      }
    }

    return false;
  }

  /// Check if CHAR tab should be visible
  /// Returns true for multiple characters OR single polyphonic characters
  bool _shouldShowCharTab() {
    if (!_isChinese) return false;

    final chineseChars = _extractChineseCharacters(widget.selectedText);

    // Show for multiple characters
    if (chineseChars.length > 1) {
      return true;
    }

    // For single character, check if it's polyphonic
    // We'll use a heuristic approach to avoid async operations in initState
    if (chineseChars.length == 1) {
      return _isLikelyPolyphonic(chineseChars.first);
    }

    return false;
  }

  /// Extract Chinese characters from text
  List<String> _extractChineseCharacters(String text) {
    final chineseChars = <String>[];
    for (int i = 0; i < text.length; i++) {
      final char = text[i];
      if (RegExp(r'[\u4e00-\u9fa5]').hasMatch(char)) {
        chineseChars.add(char);
      }
    }
    return chineseChars;
  }

  /// Heuristic check for likely polyphonic characters
  /// Based on common polyphonic characters in Chinese
  bool _isLikelyPolyphonic(String character) {
    // Common polyphonic characters that users frequently encounter
    const commonPolyphonicChars = {
      '没',
      '得',
      '的',
      '地',
      '着',
      '了',
      '中',
      '长',
      '大',
      '小',
      '好',
      '行',
      '发',
      '会',
      '要',
      '还',
      '都',
      '为',
      '和',
      '种',
      '重',
      '应',
      '便',
      '间',
      '传',
      '教',
      '数',
      '处',
      '调',
      '背',
      '当',
      '正',
      '几',
      '分',
      '切',
      '单',
      '干',
      '华',
      '结',
      '解',
      '空',
      '累',
      '量',
      '论',
      '难',
      '强',
      '任',
      '散',
      '少',
      '省',
      '识',
      '似',
      '提',
      '系',
      '相',
      '兴',
      '血',
      '压',
      '一',
      '音',
      '与',
      '语',
      '载',
      '占',
      '只',
      '转',
      '作',
      '做',
      '钻',
    };

    return commonPolyphonicChars.contains(character);
  }

  /// Build Dict tab widget
  Widget _buildDictTab() {
    return Tab(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(AdaptiveIcons.dictionary, size: 16),
          const SizedBox(width: DesignSystem.spaceXS),
          const Text('Dict'),
        ],
      ),
    );
  }

  /// Build SET/WORD tab widget
  Widget _buildSetWordTab() {
    return Tab(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(AdaptiveIcons.contentCut, size: 16),
          const SizedBox(width: DesignSystem.spaceXS),
          const Text('Set/Word'),
        ],
      ),
    );
  }

  /// Build CHAR tab widget
  Widget _buildCharTab() {
    return Tab(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(AdaptiveIcons.textFields, size: 16),
          const SizedBox(width: DesignSystem.spaceXS),
          const Text('CHAR'),
        ],
      ),
    );
  }

  /// Build Dict tab view content
  Widget _buildDictTabView() {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: ContextMenuDictionaryTab(
        text: widget.selectedText,
      ),
    );
  }

  /// Build SET/WORD tab view content
  Widget _buildSetWordTabView() {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: ContextMenuWordSegmentationTab(
        selectedText: widget.selectedText,
        bookId: widget.bookId,
        startOffset: 0,
        endOffset: widget.selectedText.length,
      ),
    );
  }

  /// Build CHAR tab view content
  Widget _buildCharTabView() {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: ContextMenuCharacterTab(
        selectedText: widget.selectedText,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final readingTextColor =
        Color(int.parse('0x${Prefs().readTheme.textColor}'));
    final theme = Theme.of(context);

    // If not Chinese text, just show dictionary tab without tabs
    if (!_isChinese) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // MD3 divider
          Divider(
            color: readingTextColor.withValues(alpha: 0.2),
            height: 0.5,
            thickness: 0.5,
          ),
          // Content-aware container for non-Chinese text
          SizedBox(
            height: 150, // Reasonable height for non-Chinese text
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: ContextMenuDictionaryTab(
                text: widget.selectedText,
              ),
            ),
          ),
        ],
      );
    }

    // For Chinese text, show tabbed interface
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // MD3 divider
        Divider(
          color: readingTextColor.withValues(alpha: 0.2),
          height: 0.5,
          thickness: 0.5,
        ),

        // Tab bar
        Container(
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: readingTextColor.withValues(alpha: 0.1),
                width: 0.5,
              ),
            ),
          ),
          child: TabBar(
            controller: _tabController,
            labelColor: theme.colorScheme.primary,
            unselectedLabelColor: readingTextColor.withValues(alpha: 0.6),
            indicatorColor: theme.colorScheme.primary,
            indicatorWeight: 2.0,
            labelStyle: theme.textTheme.labelMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
            unselectedLabelStyle: theme.textTheme.labelMedium?.copyWith(
              fontWeight: FontWeight.normal,
            ),
            tabs: _visibleTabs,
          ),
        ),

        // Content-aware tab content with proper constraints for TabBarView
        SizedBox(
          height: 200, // Reasonable default height that works with TabBarView
          child: TabBarView(
            controller: _tabController,
            children: _visibleTabViews,
          ),
        ),
      ],
    );
  }
}
