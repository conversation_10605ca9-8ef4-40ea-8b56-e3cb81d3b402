#!/bin/bash
# DassoShu Reader - Rollback Design System Automation
# Generated: 2025-07-14 16:58:17.275341

cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_16-57-58_pre-automation/lib/config/responsive_system.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/config/responsive_system.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_16-57-58_pre-automation/lib/config/navigation_system.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/config/navigation_system.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_16-57-58_pre-automation/lib/config/app_typography.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/config/app_typography.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_16-57-58_pre-automation/lib/widgets/settings/user_profile_section.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/widgets/settings/user_profile_section.dart'
cp '/Users/<USER>/PycharmProjects/dasso-reader/backups/2025-07-14_16-57-58_pre-automation/lib/widgets/common/orientation_aware_widgets.dart' '/Users/<USER>/PycharmProjects/dasso-reader/lib/widgets/common/orientation_aware_widgets.dart'

echo 'Rollback completed successfully!'
