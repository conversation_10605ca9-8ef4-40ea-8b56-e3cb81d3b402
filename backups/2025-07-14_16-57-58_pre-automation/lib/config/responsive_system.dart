import 'package:flutter/material.dart';
import 'design_system.dart';
import '../enums/dynamic_content_types.dart';

/// Comprehensive responsive system for Dasso Reader
///
/// This system provides advanced responsive utilities for:
/// - Screen size adaptability
/// - Orientation handling
/// - Dynamic content management
/// - Touch target optimization
/// - Multi-pane layouts for tablets
/// - State preservation during orientation changes
class ResponsiveSystem {
  ResponsiveSystem._();

  // =====================================================
  // SCREEN SIZE UTILITIES
  // =====================================================

  /// Get screen size - replacement for MediaQuery.of(context).size
  static Size getScreenSize(BuildContext context) =>
      MediaQuery.of(context).size;

  /// Get screen width - replacement for MediaQuery.of(context).size.width
  static double getScreenWidth(BuildContext context) =>
      MediaQuery.of(context).size.width;

  /// Get screen height - replacement for MediaQuery.of(context).size.height
  static double getScreenHeight(BuildContext context) =>
      MediaQuery.of(context).size.height;

  /// Get screen padding - replacement for MediaQuery.of(context).padding
  static EdgeInsets getScreenPadding(BuildContext context) =>
      MediaQuery.of(context).padding;

  /// Get view insets - replacement for MediaQuery.of(context).viewInsets
  static EdgeInsets getViewInsets(BuildContext context) =>
      MediaQuery.of(context).viewInsets;

  // =====================================================
  // ORIENTATION HANDLING
  // =====================================================

  /// Get current orientation
  static Orientation getOrientation(BuildContext context) =>
      MediaQuery.of(context).orientation;

  /// Check if device is in landscape mode
  static bool isLandscape(BuildContext context) =>
      getOrientation(context) == Orientation.landscape;

  /// Check if device is in portrait mode
  static bool isPortrait(BuildContext context) =>
      getOrientation(context) == Orientation.portrait;

  /// Check if orientation change should trigger layout rebuild
  static bool shouldRebuildOnOrientationChange(BuildContext context) {
    // Always rebuild for mobile and tablet devices
    if (DesignSystem.isMobile(context) || DesignSystem.isTablet(context)) {
      return true;
    }
    // Desktop can handle orientation changes more gracefully
    return false;
  }

  /// Get orientation-specific constraints for dialogs
  static BoxConstraints getOrientationAwareDialogConstraints(
    BuildContext context,
  ) {
    final screenSize = MediaQuery.of(context).size;
    final isLandscapeMode = isLandscape(context);

    if (isLandscapeMode) {
      // In landscape, use more horizontal space but limit height
      return BoxConstraints(
        maxWidth: screenSize.width * 0.8,
        maxHeight: screenSize.height * 0.9,
        minWidth: 320,
        minHeight: 200,
      );
    } else {
      // In portrait, use more vertical space
      return BoxConstraints(
        maxWidth: screenSize.width * 0.9,
        maxHeight: screenSize.height * 0.8,
        minWidth: 280,
        minHeight: 300,
      );
    }
  }

  /// Get orientation-specific bottom sheet constraints
  static BoxConstraints getOrientationAwareBottomSheetConstraints(
    BuildContext context,
  ) {
    final screenSize = MediaQuery.of(context).size;
    final isLandscapeMode = isLandscape(context);

    if (isLandscapeMode) {
      // In landscape, limit height more aggressively
      return BoxConstraints(
        maxHeight: screenSize.height * 0.7,
        minHeight: 200,
      );
    } else {
      // In portrait, can use more height
      return BoxConstraints(
        maxHeight: screenSize.height * 0.9,
        minHeight: 300,
      );
    }
  }

  /// Get adaptive column count based on orientation and device size
  static int getAdaptiveColumnCount(
    BuildContext context, {
    int mobilePortrait = 2,
    int mobileLandscape = 3,
    int tabletPortrait = 3,
    int tabletLandscape = 4,
    int desktopPortrait = 4,
    int desktopLandscape = 5,
  }) {
    final isLandscapeMode = isLandscape(context);

    if (DesignSystem.isDesktop(context) ||
        DesignSystem.isLargeDesktop(context)) {
      return isLandscapeMode ? desktopLandscape : desktopPortrait;
    } else if (DesignSystem.isTablet(context)) {
      return isLandscapeMode ? tabletLandscape : tabletPortrait;
    } else {
      return isLandscapeMode ? mobileLandscape : mobilePortrait;
    }
  }

  // =====================================================
  // TOUCH TARGET OPTIMIZATION
  // =====================================================

  /// Minimum touch target size (44dp for accessibility)
  static const double minTouchTarget = 44.0;

  /// Get adaptive touch target size based on device
  static double getAdaptiveTouchTargetSize(BuildContext context) {
    if (DesignSystem.isDesktop(context) ||
        DesignSystem.isLargeDesktop(context)) {
      return 48.0; // Slightly larger for desktop
    } else if (DesignSystem.isTablet(context)) {
      return 46.0; // Medium size for tablets
    } else {
      return minTouchTarget; // Standard size for mobile
    }
  }

  /// Ensure minimum touch target constraints
  static BoxConstraints getTouchTargetConstraints(BuildContext context) {
    final size = getAdaptiveTouchTargetSize(context);
    return BoxConstraints(
      minWidth: size,
      minHeight: size,
    );
  }

  // =====================================================
  // CONTENT DENSITY MANAGEMENT
  // =====================================================

  /// Get appropriate content density based on screen size
  static ContentDensity getAdaptiveContentDensity(BuildContext context) {
    if (DesignSystem.isLargeDesktop(context)) {
      return ContentDensity.spacious;
    } else if (DesignSystem.isDesktop(context)) {
      return ContentDensity.standard;
    } else if (DesignSystem.isTablet(context)) {
      return ContentDensity.comfortable;
    } else if (DesignSystem.isSmallPhone(context)) {
      return ContentDensity.compact;
    } else {
      return ContentDensity.comfortable;
    }
  }

  /// Get adaptive spacing based on content density
  static double getAdaptiveSpacing(BuildContext context) {
    final density = getAdaptiveContentDensity(context);
    return DesignSystem.getContentDensitySpacing(density);
  }

  // =====================================================
  // MULTI-PANE LAYOUT SUPPORT
  // =====================================================

  /// Check if device should use multi-pane layout
  static bool shouldUseMultiPaneLayout(BuildContext context) {
    return DesignSystem.isTablet(context) ||
        DesignSystem.isDesktop(context) ||
        DesignSystem.isLargeDesktop(context);
  }

  /// Get adaptive layout type
  static LayoutType getAdaptiveLayoutType(BuildContext context) {
    if (DesignSystem.isSmallPhone(context)) {
      return LayoutType.singlePane;
    } else if (DesignSystem.isMobile(context)) {
      return LayoutType.singlePane;
    } else if (DesignSystem.isLargePhone(context)) {
      return isLandscape(context) ? LayoutType.dualPane : LayoutType.singlePane;
    } else if (DesignSystem.isTablet(context)) {
      return LayoutType.dualPane;
    } else {
      return LayoutType.multiPane;
    }
  }

  // =====================================================
  // DYNAMIC CONTENT HANDLING
  // =====================================================

  /// Get adaptive text overflow handling based on content type and screen size
  static TextOverflow getAdaptiveTextOverflow(
    BuildContext context, {
    TextContentType contentType = TextContentType.general,
  }) {
    switch (contentType) {
      case TextContentType.userGenerated:
        // User content should be more visible, use clip for better readability
        return DesignSystem.isSmallPhone(context)
            ? TextOverflow.ellipsis
            : TextOverflow.clip;
      case TextContentType.translation:
        // Translations should show as much as possible
        return TextOverflow.fade;
      case TextContentType.title:
        // Titles should be truncated cleanly
        return TextOverflow.ellipsis;
      case TextContentType.general:
      default:
        if (DesignSystem.isSmallPhone(context)) {
          return TextOverflow
              .ellipsis; // More aggressive truncation on small screens
        } else {
          return TextOverflow.fade; // Gentler overflow on larger screens
        }
    }
  }

  /// Get adaptive max lines for text based on content type and screen size
  static int getAdaptiveMaxLines(
    BuildContext context, {
    int baseLines = 2,
    TextContentType contentType = TextContentType.general,
  }) {
    // Base adjustment for screen size
    int screenAdjustment = 0;
    if (DesignSystem.isDesktop(context) ||
        DesignSystem.isLargeDesktop(context)) {
      screenAdjustment = 2; // More lines on desktop
    } else if (DesignSystem.isTablet(context)) {
      screenAdjustment = 1; // Slightly more on tablet
    } else if (DesignSystem.isSmallPhone(context)) {
      screenAdjustment = -1; // Fewer lines on small phones
    }

    // Content type specific adjustments
    int contentAdjustment = 0;
    switch (contentType) {
      case TextContentType.userGenerated:
        contentAdjustment = 2; // Allow more lines for user content
        break;
      case TextContentType.translation:
        contentAdjustment = 3; // Translations often need more space
        break;
      case TextContentType.title:
        contentAdjustment = -1; // Titles should be concise
        break;
      case TextContentType.general:
      default:
        contentAdjustment = 0;
    }

    return (baseLines + screenAdjustment + contentAdjustment).clamp(1, 10);
  }

  /// Get adaptive image aspect ratio based on content type and orientation
  static double getAdaptiveImageAspectRatio(
    BuildContext context, {
    ImageContentType contentType = ImageContentType.general,
  }) {
    final isLandscapeOrientation = isLandscape(context);

    switch (contentType) {
      case ImageContentType.bookCover:
        return isLandscapeOrientation
            ? 0.7
            : 0.65; // Book covers are typically taller
      case ImageContentType.avatar:
        return 1.0; // Avatars are square
      case ImageContentType.banner:
        return isLandscapeOrientation ? 3.0 : 2.5; // Banners are wide
      case ImageContentType.thumbnail:
        return isLandscapeOrientation
            ? 1.5
            : 1.2; // Thumbnails are slightly wide
      case ImageContentType.general:
      default:
        if (isLandscapeOrientation) {
          return 16 / 9; // Wider aspect ratio in landscape
        } else {
          return 4 / 3; // Standard aspect ratio in portrait
        }
    }
  }

  /// Get adaptive image size constraints
  static BoxConstraints getAdaptiveImageConstraints(
    BuildContext context, {
    ImageContentType contentType = ImageContentType.general,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = DesignSystem.isSmallPhone(context);

    switch (contentType) {
      case ImageContentType.bookCover:
        final maxWidth = isSmallScreen ? screenWidth * 0.3 : screenWidth * 0.25;
        return BoxConstraints(
          maxWidth: maxWidth,
          maxHeight: maxWidth /
              getAdaptiveImageAspectRatio(context, contentType: contentType),
          minWidth: 80,
          minHeight: 120,
        );
      case ImageContentType.avatar:
        final size = isSmallScreen ? 40.0 : 48.0;
        return BoxConstraints.tight(Size(size, size));
      case ImageContentType.banner:
        return BoxConstraints(
          maxWidth: screenWidth,
          maxHeight: screenWidth /
              getAdaptiveImageAspectRatio(context, contentType: contentType),
          minHeight: 120,
        );
      case ImageContentType.thumbnail:
        final maxWidth = isSmallScreen ? 80.0 : 120.0;
        return BoxConstraints(
          maxWidth: maxWidth,
          maxHeight: maxWidth /
              getAdaptiveImageAspectRatio(context, contentType: contentType),
          minWidth: 60,
          minHeight: 60,
        );
      case ImageContentType.general:
      default:
        return BoxConstraints(
          maxWidth: screenWidth * 0.9,
          maxHeight: screenWidth *
              0.9 /
              getAdaptiveImageAspectRatio(context, contentType: contentType),
          minWidth: 100,
          minHeight: 100,
        );
    }
  }

  /// Get adaptive list item constraints for dynamic content
  static BoxConstraints getAdaptiveListItemConstraints(BuildContext context) {
    final minHeight = DesignSystem.isSmallPhone(context) ? 56.0 : 64.0;
    return BoxConstraints(
      minHeight: minHeight,
      maxHeight: double.infinity,
    );
  }

  /// Get adaptive grid item constraints for dynamic content
  static BoxConstraints getAdaptiveGridItemConstraints(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final columnCount = getAdaptiveColumnCount(context);
    final itemWidth =
        (screenWidth - (DesignSystem.spaceM * (columnCount + 1))) / columnCount;

    return BoxConstraints(
      minWidth: itemWidth,
      maxWidth: itemWidth,
      minHeight: itemWidth * 0.8, // Minimum aspect ratio
      maxHeight: itemWidth * 2.0, // Maximum aspect ratio
    );
  }

  /// Get adaptive content padding based on screen size
  static EdgeInsets getAdaptiveContentPadding(BuildContext context) {
    if (DesignSystem.isLargeDesktop(context)) {
      return const EdgeInsets.all(DesignSystem.spaceXXL);
    } else if (DesignSystem.isDesktop(context)) {
      return const EdgeInsets.all(DesignSystem.spaceXL);
    } else if (DesignSystem.isTablet(context)) {
      return const EdgeInsets.all(DesignSystem.spaceL);
    } else {
      return const EdgeInsets.all(DesignSystem.spaceM);
    }
  }

  /// Get orientation-specific reading interface layout
  static EdgeInsets getOrientationAwareReadingPadding(BuildContext context) {
    final isLandscapeMode = isLandscape(context);

    if (DesignSystem.isDesktop(context)) {
      return isLandscapeMode
          ? const EdgeInsets.symmetric(horizontal: 48, vertical: 16)
          : const EdgeInsets.symmetric(horizontal: 32, vertical: 24);
    } else if (DesignSystem.isTablet(context)) {
      return isLandscapeMode
          ? const EdgeInsets.symmetric(horizontal: 32, vertical: 12)
          : const EdgeInsets.symmetric(horizontal: 24, vertical: 16);
    } else {
      return isLandscapeMode
          ? const EdgeInsets.symmetric(horizontal: 16, vertical: 8)
          : const EdgeInsets.symmetric(horizontal: 16, vertical: 12);
    }
  }

  /// Get orientation-specific app bar height
  static double getOrientationAwareAppBarHeight(BuildContext context) {
    final isLandscapeMode = isLandscape(context);

    if (DesignSystem.isMobile(context) && isLandscapeMode) {
      return 48.0; // Shorter app bar in mobile landscape
    } else {
      return 56.0; // Standard app bar height
    }
  }

  /// Get orientation-specific bottom navigation height
  static double getOrientationAwareBottomNavHeight(BuildContext context) {
    final isLandscapeMode = isLandscape(context);

    if (DesignSystem.isMobile(context) && isLandscapeMode) {
      return 56.0; // Shorter bottom nav in mobile landscape
    } else {
      return 80.0; // Standard bottom nav height
    }
  }

  /// Check if should use compact layout in current orientation
  static bool shouldUseCompactLayout(BuildContext context) {
    final isLandscapeMode = isLandscape(context);

    // Use compact layout for mobile landscape or small screens
    return (DesignSystem.isMobile(context) && isLandscapeMode) ||
        DesignSystem.isSmallPhone(context);
  }

  /// Get orientation-specific form field spacing
  static double getOrientationAwareFormSpacing(BuildContext context) {
    final isLandscapeMode = isLandscape(context);

    if (shouldUseCompactLayout(context)) {
      return 12.0; // Tighter spacing in compact mode
    } else if (isLandscapeMode) {
      return 16.0; // Standard spacing in landscape
    } else {
      return 20.0; // More generous spacing in portrait
    }
  }

  // =====================================================
  // NAVIGATION ADAPTATIONS
  // =====================================================

  /// Get adaptive navigation type
  static NavigationType getAdaptiveNavigationType(BuildContext context) {
    if (DesignSystem.isDesktop(context) ||
        DesignSystem.isLargeDesktop(context)) {
      return NavigationType.rail;
    } else if (DesignSystem.isTablet(context) && isLandscape(context)) {
      return NavigationType.rail;
    } else {
      return NavigationType.bottom;
    }
  }

  /// Check if should use extended navigation rail
  static bool shouldUseExtendedRail(BuildContext context) {
    return DesignSystem.isLargeDesktop(context) ||
        (DesignSystem.isDesktop(context) && isLandscape(context));
  }

  // =====================================================
  // DIALOG AND MODAL ADAPTATIONS
  // =====================================================

  /// Get adaptive dialog constraints
  static BoxConstraints getAdaptiveDialogConstraints(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    if (DesignSystem.isDesktop(context) ||
        DesignSystem.isLargeDesktop(context)) {
      return BoxConstraints(
        maxWidth: 600,
        maxHeight: screenSize.height * 0.8,
        minWidth: 400,
      );
    } else if (DesignSystem.isTablet(context)) {
      return BoxConstraints(
        maxWidth: screenSize.width * 0.7,
        maxHeight: screenSize.height * 0.8,
        minWidth: 300,
      );
    } else {
      return BoxConstraints(
        maxWidth: screenSize.width * 0.9,
        maxHeight: screenSize.height * 0.9,
        minWidth: 280,
      );
    }
  }

  /// Get adaptive bottom sheet height
  static double getAdaptiveBottomSheetHeight(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;

    if (DesignSystem.isDesktop(context) ||
        DesignSystem.isLargeDesktop(context)) {
      return screenHeight * 0.5; // Smaller on desktop
    } else if (DesignSystem.isTablet(context)) {
      return screenHeight * 0.6; // Medium on tablet
    } else {
      return screenHeight * 0.75; // Larger on mobile
    }
  }

  // =====================================================
  // GRID AND LIST ADAPTATIONS
  // =====================================================

  /// Get adaptive grid spacing
  static double getAdaptiveGridSpacing(BuildContext context) {
    if (DesignSystem.isLargeDesktop(context)) {
      return DesignSystem.spaceXL;
    } else if (DesignSystem.isDesktop(context)) {
      return DesignSystem.spaceL;
    } else if (DesignSystem.isTablet(context)) {
      return DesignSystem.spaceM;
    } else {
      return DesignSystem.spaceS;
    }
  }

  /// Get adaptive list item height
  static double getAdaptiveListItemHeight(BuildContext context) {
    if (DesignSystem.isDesktop(context) ||
        DesignSystem.isLargeDesktop(context)) {
      return 72.0; // Taller items on desktop
    } else if (DesignSystem.isTablet(context)) {
      return 64.0; // Medium height on tablet
    } else if (DesignSystem.isSmallPhone(context)) {
      return 48.0; // Compact on small phones
    } else {
      return 56.0; // Standard on mobile
    }
  }
}

/// Layout types for responsive design
enum LayoutType {
  singlePane,
  dualPane,
  multiPane,
}

/// Navigation types for responsive design
enum NavigationType {
  bottom,
  rail,
  drawer,
}
