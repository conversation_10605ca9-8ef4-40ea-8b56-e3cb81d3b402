import 'dart:math' as math;
import 'package:flutter/material.dart' hide NavigationDestination;
import 'package:dasso_reader/config/navigation_system.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/app_icons.dart';
import 'package:flutter/material.dart' as material;

/// Enhanced Navigation Widgets for Dasso Reader
///
/// This file provides enhanced navigation components that use the NavigationSystem
/// for consistent, accessible, and platform-adaptive navigation.

// =====================================================
// ENHANCED TAB BAR
// =====================================================

/// Enhanced TabBar with improved visual feedback and accessibility
class EnhancedTabBar extends StatelessWidget implements PreferredSizeWidget {
  final List<NavigationDestination> destinations;
  final int currentIndex;
  final ValueChanged<int> onDestinationSelected;
  final bool isScrollable;
  final Color? backgroundColor;
  final Color? indicatorColor;

  const EnhancedTabBar({
    super.key,
    required this.destinations,
    required this.currentIndex,
    required this.onDestinationSelected,
    this.isScrollable = false,
    this.backgroundColor,
    this.indicatorColor,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: colorScheme.outlineVariant.withAlpha(128),
            width: DesignSystem.spaceMicro,
          ),
        ),
      ),
      child: TabBar(
        isScrollable: isScrollable,
        tabs: destinations.asMap().entries.map((entry) {
          final index = entry.key;
          final destination = entry.value;
          final isSelected = index == currentIndex;

          return NavigationSystem.createNavigationTab(
            context: context,
            destination: destination,
            isSelected: isSelected,
            onTap: () => onDestinationSelected(index),
          );
        }).toList(),
        labelColor: indicatorColor ?? colorScheme.primary,
        unselectedLabelColor: colorScheme.onSurfaceVariant,
        indicatorColor: indicatorColor ?? colorScheme.primary,
        indicatorWeight: DesignSystem.spaceXS - DesignSystem.spaceMicro,
        indicatorSize: TabBarIndicatorSize.tab,
        splashBorderRadius: BorderRadius.circular(DesignSystem.radiusM),
        overlayColor: WidgetStateProperty.resolveWith<Color?>((states) {
          if (states.contains(WidgetState.hovered)) {
            return colorScheme.primary.withAlpha((0.08 * 255).round());
          }
          if (states.contains(WidgetState.pressed)) {
            return colorScheme.primary.withAlpha((0.12 * 255).round());
          }
          return null;
        }),
        onTap: (index) {
          NavigationSystem.provideFeedback(NavigationFeedbackType.selection);
          onDestinationSelected(index);
        },
      ),
    );
  }

  @override
  Size get preferredSize =>
      const Size.fromHeight(DesignSystem.spaceXXL + DesignSystem.spaceL);
}

// =====================================================
// ENHANCED NAVIGATION RAIL
// =====================================================

/// Enhanced NavigationRail with improved visual feedback and responsive behavior
class EnhancedNavigationRail extends StatelessWidget {
  final List<NavigationDestination> destinations;
  final int currentIndex;
  final ValueChanged<int> onDestinationSelected;
  final bool extended;
  final Widget? leading;
  final Widget? trailing;
  final Color? backgroundColor;

  const EnhancedNavigationRail({
    super.key,
    required this.destinations,
    required this.currentIndex,
    required this.onDestinationSelected,
    this.extended = false,
    this.leading,
    this.trailing,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? colorScheme.surface,
        border: Border(
          right: BorderSide(
            color: colorScheme.outlineVariant.withAlpha(128),
            width: DesignSystem.spaceMicro,
          ),
        ),
      ),
      child: NavigationRail(
        extended: extended,
        selectedIndex: currentIndex,
        onDestinationSelected: (index) {
          NavigationSystem.provideFeedback(NavigationFeedbackType.selection);
          onDestinationSelected(index);
        },
        leading: leading,
        trailing: trailing,
        destinations: destinations.map((destination) {
          return NavigationRailDestination(
            icon: Icon(destination.icon),
            selectedIcon: Icon(destination.getIcon(selected: true)),
            label: Text(destination.getLabel(context)),
          );
        }).toList(),
        labelType: extended
            ? NavigationRailLabelType.none
            : NavigationRailLabelType.all,
        backgroundColor: Colors.transparent,
        selectedIconTheme: IconThemeData(
          color: colorScheme.primary,
          size: AppIcons.sizeM,
        ),
        unselectedIconTheme: IconThemeData(
          color: colorScheme.onSurfaceVariant,
          size: AppIcons.sizeM,
        ),
        selectedLabelTextStyle: TextStyle(
          color: colorScheme.primary,
          fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600),
          fontSize: DesignSystem.fontSizeS,
        ),
        unselectedLabelTextStyle: TextStyle(
          color: colorScheme.onSurfaceVariant,
          fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w500),
          fontSize: 12.0,
        ),
        indicatorColor: colorScheme.primaryContainer,
        indicatorShape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignSystem.radiusM),
        ),
      ),
    );
  }
}

// =====================================================
// ENHANCED BOTTOM NAVIGATION BAR
// =====================================================

/// Enhanced BottomNavigationBar with improved visual feedback and adaptive height
class EnhancedBottomNavigationBar extends StatelessWidget {
  final List<NavigationDestination> destinations;
  final int currentIndex;
  final ValueChanged<int> onDestinationSelected;
  final Color? backgroundColor;
  final double? height;
  final EdgeInsetsGeometry? margin;

  const EnhancedBottomNavigationBar({
    super.key,
    required this.destinations,
    required this.currentIndex,
    required this.onDestinationSelected,
    this.backgroundColor,
    this.height,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    // Calculate adaptive height matching your TabBar implementation
    final adaptiveHeight = height ?? _calculateAdaptiveHeight(context);

    // Use NavigationBar for Material 3 design with enhanced customization
    return Container(
      height: adaptiveHeight,
      margin: margin,
      child: NavigationBar(
        selectedIndex: currentIndex,
        onDestinationSelected: (index) {
          NavigationSystem.provideFeedback(NavigationFeedbackType.selection);
          onDestinationSelected(index);
        },
        backgroundColor: backgroundColor ?? colorScheme.surface,
        indicatorColor: colorScheme.primaryContainer,
        indicatorShape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignSystem.radiusM),
        ),
        destinations: destinations.map((destination) {
          return material.NavigationDestination(
            icon: Icon(
              destination.icon,
              size: AppIcons.sizeM, // Consistent with your design system
            ),
            selectedIcon: Icon(
              destination.getIcon(selected: true),
              size: AppIcons.sizeM,
            ),
            label: destination.getLabel(context),
            tooltip: destination.getTooltip(context),
          );
        }).toList(),
        labelBehavior: NavigationDestinationLabelBehavior.alwaysShow,
        animationDuration: DesignSystem.durationFast,
        // Enhanced elevation for better visual hierarchy with manufacturer adjustments
        elevation: DesignSystem.getAdjustedElevation(DesignSystem.elevationS),
      ),
    );
  }

  /// Calculate adaptive height matching the TabBar implementation
  double _calculateAdaptiveHeight(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final devicePixelRatio = mediaQuery.devicePixelRatio;
    final textScaler = mediaQuery.textScaler;

    const baseHeight = DesignSystem.spaceXXL +
        DesignSystem.spaceL +
        DesignSystem.spaceTiny; // Match Material Design standard
    const minHeight = DesignSystem.widgetMinTouchTarget +
        DesignSystem.spaceXS; // Accessibility minimum

    // Scale for device density
    double densityFactor = (devicePixelRatio / 3.0).clamp(0.85, 1.25);

    // Scale for text size
    double textFactor = textScaler.scale(1.0).clamp(0.9, 1.3);

    // Device-specific adjustments
    double deviceFactor = 1.0;
    if (DesignSystem.isSmallPhone(context)) {
      deviceFactor = 0.9;
    } else if (DesignSystem.isTablet(context)) {
      deviceFactor = 1.1;
    }

    double calculatedHeight =
        baseHeight * densityFactor * textFactor * deviceFactor;

    // Ensure accessibility compliance
    return math.max(calculatedHeight, minHeight);
  }
}

// =====================================================
// ADAPTIVE NAVIGATION WRAPPER
// =====================================================

/// Adaptive navigation wrapper that chooses the appropriate navigation pattern
/// based on screen size and platform
class AdaptiveNavigationWrapper extends StatelessWidget {
  final List<NavigationDestination> destinations;
  final int currentIndex;
  final ValueChanged<int> onDestinationSelected;
  final Widget child;
  final Widget? leading;
  final Widget? trailing;
  final bool forceBottomNavigation;

  const AdaptiveNavigationWrapper({
    super.key,
    required this.destinations,
    required this.currentIndex,
    required this.onDestinationSelected,
    required this.child,
    this.leading,
    this.trailing,
    this.forceBottomNavigation = false,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final shouldUseRail = !forceBottomNavigation &&
            (constraints.maxWidth > DesignSystem.breakpointTablet ||
                (DesignSystem.isTablet(context) &&
                    MediaQuery.of(context).orientation ==
                        Orientation.landscape));

        if (shouldUseRail) {
          // Use NavigationRail for larger screens
          final extended =
              constraints.maxWidth > DesignSystem.breakpointDesktop;

          return Scaffold(
            body: Row(
              children: [
                EnhancedNavigationRail(
                  destinations: destinations,
                  currentIndex: currentIndex,
                  onDestinationSelected: onDestinationSelected,
                  extended: extended,
                  leading: leading,
                  trailing: trailing,
                ),
                Expanded(child: child),
              ],
            ),
          );
        } else {
          // Use BottomNavigationBar for smaller screens
          return Scaffold(
            body: child,
            bottomNavigationBar: EnhancedBottomNavigationBar(
              destinations: destinations,
              currentIndex: currentIndex,
              onDestinationSelected: onDestinationSelected,
            ),
          );
        }
      },
    );
  }
}
