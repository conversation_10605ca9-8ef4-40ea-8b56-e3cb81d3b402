#!/usr/bin/env python3
"""
DassoShu Reader - Safe Design System Automation Fixer v2
Phase 1: Conservative direct pattern replacements for low-risk violations

This script safely automates the most common Design System violations
with 100% safe, const-compatible direct pattern replacements.
"""

import os
import re
import shutil
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import json

class SafeDesignSystemFixer:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.backup_dir = self.project_root / "backups" / f"{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}_pre-automation"
        self.changes_log = []
        
        # Phase 1: 100% Safe Direct Replacements (const-compatible only)
        self.SAFE_REPLACEMENTS = {
            # EdgeInsets.all patterns (const-safe values only)
            'EdgeInsets.all(8.0)': 'EdgeInsets.all(DesignSystem.spaceS)',
            'EdgeInsets.all(8)': 'EdgeInsets.all(DesignSystem.spaceS)',
            'EdgeInsets.all(16.0)': 'EdgeInsets.all(DesignSystem.spaceM)',
            'EdgeInsets.all(16)': 'EdgeInsets.all(DesignSystem.spaceM)',

            'EdgeInsets.all(24.0)': 'EdgeInsets.all(DesignSystem.spaceL)',
            'EdgeInsets.all(24)': 'EdgeInsets.all(DesignSystem.spaceL)',
            'EdgeInsets.all(32.0)': 'EdgeInsets.all(DesignSystem.spaceXL)',
            'EdgeInsets.all(32)': 'EdgeInsets.all(DesignSystem.spaceXL)',
            
            # SizedBox height patterns (const-safe values only)
            'SizedBox(height: 8)': 'SizedBox(height: DesignSystem.spaceS)',
            'SizedBox(height: 16)': 'SizedBox(height: DesignSystem.spaceM)',
            'SizedBox(height: 24)': 'SizedBox(height: DesignSystem.spaceL)',
            'SizedBox(height: 32)': 'SizedBox(height: DesignSystem.spaceXL)',
            
            # SizedBox width patterns (const-safe values only)
            'SizedBox(width: 8)': 'SizedBox(width: DesignSystem.spaceS)',
            'SizedBox(width: 16)': 'SizedBox(width: DesignSystem.spaceM)',
            'SizedBox(width: 24)': 'SizedBox(width: DesignSystem.spaceL)',
            'SizedBox(width: 32)': 'SizedBox(width: DesignSystem.spaceXL)',
            
            # BorderRadius patterns (const-safe values only)
            'BorderRadius.circular(8.0)': 'BorderRadius.circular(DesignSystem.radiusS)',
            'BorderRadius.circular(8)': 'BorderRadius.circular(DesignSystem.radiusS)',
            'BorderRadius.circular(16.0)': 'BorderRadius.circular(DesignSystem.radiusM)',
            'BorderRadius.circular(16)': 'BorderRadius.circular(DesignSystem.radiusM)',

            
            # Symmetric EdgeInsets patterns (const-safe values only)
            'EdgeInsets.symmetric(horizontal: 8.0)': 'EdgeInsets.symmetric(horizontal: DesignSystem.spaceS)',
            'EdgeInsets.symmetric(horizontal: 8)': 'EdgeInsets.symmetric(horizontal: DesignSystem.spaceS)',
            'EdgeInsets.symmetric(horizontal: 16.0)': 'EdgeInsets.symmetric(horizontal: DesignSystem.spaceM)',
            'EdgeInsets.symmetric(horizontal: 16)': 'EdgeInsets.symmetric(horizontal: DesignSystem.spaceM)',
            'EdgeInsets.symmetric(vertical: 8.0)': 'EdgeInsets.symmetric(vertical: DesignSystem.spaceS)',
            'EdgeInsets.symmetric(vertical: 8)': 'EdgeInsets.symmetric(vertical: DesignSystem.spaceS)',
            'EdgeInsets.symmetric(vertical: 16.0)': 'EdgeInsets.symmetric(vertical: DesignSystem.spaceM)',
            'EdgeInsets.symmetric(vertical: 16)': 'EdgeInsets.symmetric(vertical: DesignSystem.spaceM)',
        }
        
        # Files that require DesignSystem import
        self.files_needing_import = set()

    def create_backup(self, file_path: Path) -> bool:
        """Create backup of file before modification"""
        try:
            backup_path = self.backup_dir / file_path.relative_to(self.project_root)
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(file_path, backup_path)
            return True
        except Exception as e:
            print(f"❌ Failed to create backup for {file_path}: {e}")
            return False

    def validate_dart_syntax(self, file_path: Path) -> bool:
        """Validate Dart file syntax using dart analyze"""
        try:
            result = subprocess.run(
                ['dart', 'analyze', str(file_path)],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            return result.returncode == 0
        except Exception as e:
            print(f"❌ Syntax validation failed for {file_path}: {e}")
            return False

    def has_design_system_import(self, content: str) -> bool:
        """Check if file already imports DesignSystem"""
        return "import 'package:dasso_reader/config/design_system.dart'" in content

    def add_design_system_import(self, content: str) -> str:
        """Add DesignSystem import if needed"""
        if self.has_design_system_import(content):
            return content
            
        # Find the last import statement
        import_pattern = r"import\s+['\"][^'\"]+['\"];"
        imports = list(re.finditer(import_pattern, content))
        
        if imports:
            last_import = imports[-1]
            insert_pos = last_import.end()
            new_import = "\nimport 'package:dasso_reader/config/design_system.dart';"
            return content[:insert_pos] + new_import + content[insert_pos:]
        else:
            # No imports found, add at the beginning
            return "import 'package:dasso_reader/config/design_system.dart';\n" + content

    def apply_safe_fixes(self, file_path: Path, dry_run: bool = False) -> Tuple[int, List[str]]:
        """Apply safe fixes to a single file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            content = original_content
            changes_made = []
            
            # Apply safe replacements
            for pattern, replacement in self.SAFE_REPLACEMENTS.items():
                if pattern in content:
                    content = content.replace(pattern, replacement)
                    changes_made.append(f"{pattern} → {replacement}")
                    self.files_needing_import.add(file_path)
            
            # Add DesignSystem import if changes were made
            if changes_made and file_path in self.files_needing_import:
                content = self.add_design_system_import(content)
            
            # Only proceed if changes were made
            if content != original_content:
                if dry_run:
                    print(f"🔍 DRY RUN - Would fix {len(changes_made)} violations in {file_path}")
                    for change in changes_made:
                        print(f"    {change}")
                    return len(changes_made), changes_made
                
                # Create backup before modifying
                if not self.create_backup(file_path):
                    return 0, []
                
                # Write changes
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                # Validate syntax
                if not self.validate_dart_syntax(file_path):
                    # Restore from backup if syntax is invalid
                    backup_path = self.backup_dir / file_path.relative_to(self.project_root)
                    shutil.copy2(backup_path, file_path)
                    print(f"❌ Syntax validation failed for {file_path}, restored from backup")
                    return 0, []
                
                print(f"✅ Fixed {len(changes_made)} violations in {file_path}")
                for change in changes_made:
                    print(f"    {change}")
                
                return len(changes_made), changes_made
            
            return 0, []
            
        except Exception as e:
            print(f"❌ Error processing {file_path}: {e}")
            return 0, []

    def get_dart_files_with_violations(self) -> List[Path]:
        """Get list of Dart files that likely have Design System violations"""
        dart_files = []
        
        # Focus on core application files
        core_paths = [
            'lib/page',
            'lib/widgets',
            'lib/service'
        ]
        
        for core_path in core_paths:
            path = self.project_root / core_path
            if path.exists():
                dart_files.extend(path.rglob('*.dart'))
        
        return dart_files

    def run_cross_platform_analyzer(self) -> Optional[Dict]:
        """Run the cross-platform analyzer and return results"""
        try:
            result = subprocess.run(
                ['dart', 'run', 'scripts/cross_platform_analyzer.dart'],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            if result.returncode == 0:
                # Parse the output to extract Design System violation count
                output = result.stdout
                design_system_match = re.search(r'🎯 Design System \((\d+)\)', output)
                if design_system_match:
                    return {
                        'design_system_violations': int(design_system_match.group(1)),
                        'output': output
                    }
            
            return None
        except Exception as e:
            print(f"❌ Failed to run cross-platform analyzer: {e}")
            return None

    def generate_rollback_script(self):
        """Generate rollback script to undo all changes"""
        rollback_script = self.backup_dir / "rollback.sh"
        
        with open(rollback_script, 'w') as f:
            f.write("#!/bin/bash\n")
            f.write("# DassoShu Reader - Rollback Design System Automation\n")
            f.write(f"# Generated: {datetime.now()}\n\n")
            
            for backup_file in self.backup_dir.rglob('*.dart'):
                original_file = self.project_root / backup_file.relative_to(self.backup_dir)
                f.write(f"cp '{backup_file}' '{original_file}'\n")
            
            f.write("\necho 'Rollback completed successfully!'\n")
        
        rollback_script.chmod(0o755)
        print(f"📝 Rollback script created: {rollback_script}")

    def process_files(self, files: List[Path], dry_run: bool = False) -> Dict:
        """Process multiple files and return summary"""
        total_fixes = 0
        files_modified = 0
        all_changes = []
        
        print(f"🔧 Processing {len(files)} files...")
        
        for file_path in files:
            fixes_count, changes = self.apply_safe_fixes(file_path, dry_run)
            if fixes_count > 0:
                total_fixes += fixes_count
                files_modified += 1
                all_changes.extend([(str(file_path), change) for change in changes])
        
        return {
            'total_fixes': total_fixes,
            'files_modified': files_modified,
            'all_changes': all_changes
        }

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Safe Design System Automation Fixer v2')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be changed without making changes')
    parser.add_argument('--file', type=str, help='Process single file')
    parser.add_argument('--batch', action='store_true', help='Process all files with violations')
    parser.add_argument('--test', action='store_true', help='Test on small subset of files')
    
    args = parser.parse_args()
    
    # Get project root (assume script is in scripts/ directory)
    project_root = Path(__file__).parent.parent
    fixer = SafeDesignSystemFixer(str(project_root))
    
    print("🚀 DassoShu Reader - Safe Design System Automation v2")
    print("=" * 55)
    
    if args.file:
        # Process single file
        file_path = project_root / args.file
        if file_path.exists():
            fixes, changes = fixer.apply_safe_fixes(file_path, args.dry_run)
            print(f"✅ Processed {file_path}: {fixes} fixes applied")
        else:
            print(f"❌ File not found: {file_path}")
        return
    
    # Get baseline violation count
    print("📊 Running baseline analysis...")
    baseline = fixer.run_cross_platform_analyzer()
    if baseline:
        print(f"📈 Baseline: {baseline['design_system_violations']} Design System violations")
    
    # Get files to process
    dart_files = fixer.get_dart_files_with_violations()
    
    if args.test:
        # Test on small subset (first 5 files)
        test_files = dart_files[:5]
        print(f"🧪 TEST MODE: Processing {len(test_files)} files")
        results = fixer.process_files(test_files, args.dry_run)
    elif args.batch:
        # Process all files
        print(f"🔄 BATCH MODE: Processing {len(dart_files)} files")
        results = fixer.process_files(dart_files, args.dry_run)
    else:
        print("❌ Please specify --file, --test, or --batch mode")
        return
    
    # Generate rollback script if changes were made
    if not args.dry_run and results['files_modified'] > 0:
        fixer.generate_rollback_script()
    
    # Run post-automation analysis
    if not args.dry_run and results['total_fixes'] > 0:
        print("\n📊 Running post-automation analysis...")
        post_analysis = fixer.run_cross_platform_analyzer()
        if post_analysis and baseline:
            violations_fixed = baseline['design_system_violations'] - post_analysis['design_system_violations']
            print(f"📈 Results: {violations_fixed} violations fixed automatically")
            print(f"📈 Remaining: {post_analysis['design_system_violations']} Design System violations")
    
    # Summary
    print("\n" + "=" * 55)
    print("📋 AUTOMATION SUMMARY")
    print("=" * 55)
    print(f"Files processed: {len(dart_files) if args.batch else (5 if args.test else 1)}")
    print(f"Files modified: {results['files_modified']}")
    print(f"Total fixes applied: {results['total_fixes']}")
    
    if args.dry_run:
        print("🔍 DRY RUN - No actual changes made")
    else:
        print(f"💾 Backup created: {fixer.backup_dir}")
        print("✅ Automation completed successfully!")

if __name__ == "__main__":
    main()
