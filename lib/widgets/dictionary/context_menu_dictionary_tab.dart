import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/models/dictionary/dictionary_entry.dart';
import 'package:dasso_reader/page/dictionary_page.dart';
import 'package:dasso_reader/service/dictionary/dictionary_service.dart';
import 'package:dasso_reader/service/dictionary/online_dictionary_service.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:pinyin/pinyin.dart';
import 'package:dasso_reader/config/design_system.dart';

/// A specialized dictionary component specifically for use in the context menu
/// This provides separation of concerns from the main dictionary tab
class ContextMenuDictionaryTab extends StatefulWidget {
  /// The text to look up in the dictionary
  final String text;

  /// Callback to close the context menu overlay
  final VoidCallback? onClose;

  const ContextMenuDictionaryTab({
    super.key,
    required this.text,
    this.onClose,
  });

  @override
  State<ContextMenuDictionaryTab> createState() =>
      _ContextMenuDictionaryTabState();
}

class _ContextMenuDictionaryTabState extends State<ContextMenuDictionaryTab> {
  bool _isLoading = true;
  List<DictionaryEntry> _entries = [];
  bool _hasExactMatch =
      false; // Track if we found exact match for the complete text

  // Services
  final OnlineDictionaryService _onlineDictionaryService =
      OnlineDictionaryService();

  @override
  void initState() {
    super.initState();
    _initDictionary();
  }

  Future<void> _initDictionary() async {
    try {
      // Add a timeout to ensure we never get stuck loading
      // Reduced timeout for better responsiveness
      await Future.any([
        _performLookup(), // Actual lookup operation
        // 1-second timeout - shorter for better UX in context menu
        Future.delayed(const Duration(seconds: 1), () => false),
      ]);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      AnxLog.severe('Error initializing dictionary service: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Perform the actual dictionary lookup with enhanced polyphonic character support
  Future<bool> _performLookup() async {
    // Pre-initialize the dictionary service
    await DictionaryService().initialize();

    // Reset the exact match flag
    _hasExactMatch = false;

    // Try to lookup the word
    if (_isChinese(widget.text)) {
      _entries = await DictionaryService().lookupChineseAll(widget.text);

      // If we found entries for the complete text, it's an exact match
      if (_entries.isNotEmpty) {
        _hasExactMatch = true;
      } else {
        // If no entries found for the whole text, try individual character lookups
        // This is especially useful for polyphonic characters in compound words
        final allCharacterEntries = <DictionaryEntry>[];
        final processedChars = <String>{};

        // Look up each unique Chinese character
        for (int i = 0; i < widget.text.length; i++) {
          final char = widget.text[i];
          if (_isChinese(char) && !processedChars.contains(char)) {
            processedChars.add(char);
            final charEntries =
                await DictionaryService().lookupChineseAll(char);
            allCharacterEntries.addAll(charEntries);
          }
        }

        if (allCharacterEntries.isNotEmpty) {
          _entries = allCharacterEntries;
          // This is NOT an exact match - just individual character lookups
          _hasExactMatch = false;
        } else {
          // Final fallback: try single lookup
          final singleEntry =
              await DictionaryService().lookupChinese(widget.text);
          if (singleEntry != null) {
            _entries = [singleEntry];
            _hasExactMatch = true; // Single lookup counts as exact match
          }
        }
      }

      // Check if we only have fallback entries (with "No definition available")
      if (_entries.length == 1 &&
          _entries.first.definitions.length == 1 &&
          _entries.first.definitions[0] == 'No definition available') {
        // This is a fallback entry, so we should show our custom UI
        _hasExactMatch = false;
        return false;
      }
    } else {
      // For non-Chinese text, could implement English lookup here
      _entries = [];
      _hasExactMatch = false;
    }

    return _entries.isNotEmpty;
  }

  /// Check if the text is Chinese
  bool _isChinese(String text) {
    // Simple check: if the text contains any Chinese characters
    return RegExp(r'[\u4e00-\u9fa5]').hasMatch(text);
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Center(
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Display the text being looked up
              Text(
                widget.text,
                style: Theme.of(context).textTheme.titleMedium,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: DesignSystem.spaceS),
              // Loading indicator with short text
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  const SizedBox(width: DesignSystem.spaceS),
                  Text(
                    'Looking up...',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    }

    // Only show dictionary view with definitions if we have an exact match
    // Individual character lookups should not show definitions
    if (_entries.isNotEmpty && _hasExactMatch) {
      return _buildDictionaryView();
    }

    // If no exact match found, show simple text and pinyin only
    return _buildSimpleTextAndPinyinView();
  }

  /// Build a simple view showing only the selected text and pinyin
  /// No definitions - user can use translator and CHAR/Set/Word tabs for detailed info
  Widget _buildSimpleTextAndPinyinView() {
    final theme = Theme.of(context);

    // Generate pinyin for the text if it's Chinese
    String pinyinText = '';
    try {
      if (_isChinese(widget.text)) {
        pinyinText = PinyinHelper.getPinyin(
          widget.text,
          separator: ' ',
          format: PinyinFormat.WITH_TONE_MARK,
        );
      }
    } catch (e) {
      AnxLog.warning('Error generating pinyin for not found view: $e');
      pinyinText = '';
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with selected text and action buttons
          Row(
            children: [
              // Selected text display - allow multiple lines for long paragraphs
              Expanded(
                child: Text(
                  widget.text,
                  style: theme.textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                  softWrap: true, // Allow text wrapping for long paragraphs
                ),
              ),

              const SizedBox(width: 12),

              // Compact action buttons
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Brush button (stroke animation practice) - first position
                  if (_isChinese(widget.text))
                    IconButton(
                      icon: Icon(AdaptiveIcons.brush),
                      iconSize: 18,
                      visualDensity: VisualDensity.compact,
                      padding: const EdgeInsets.all(4),
                      constraints:
                          const BoxConstraints(minWidth: 32, minHeight: 32),
                      onPressed: () {
                        // Close the context menu overlay before navigation
                        widget.onClose?.call();
                        Navigator.push(
                          context,
                          MaterialPageRoute<void>(
                            builder: (context) =>
                                BatchCharacterStudyPage(text: widget.text),
                          ),
                        );
                      },
                      tooltip: 'Stroke Order Practice',
                    ),

                  // Copy button - second position
                  IconButton(
                    icon: Icon(AdaptiveIcons.copy),
                    iconSize: 18,
                    visualDensity: VisualDensity.compact,
                    padding: const EdgeInsets.all(4),
                    constraints:
                        const BoxConstraints(minWidth: 32, minHeight: 32),
                    onPressed: () {
                      Clipboard.setData(ClipboardData(text: widget.text));
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(L10n.of(context).notes_page_copied),
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    },
                    tooltip: L10n.of(context).common_copy,
                  ),

                  // Pronunciation button - third position
                  if (_isChinese(widget.text))
                    IconButton(
                      icon: Icon(AdaptiveIcons.volume),
                      iconSize: 18,
                      visualDensity: VisualDensity.compact,
                      padding: const EdgeInsets.all(4),
                      constraints:
                          const BoxConstraints(minWidth: 32, minHeight: 32),
                      onPressed: () async {
                        try {
                          await _onlineDictionaryService
                              .playPronunciation(widget.text, context: context);
                        } catch (e) {
                          if (!mounted) return;
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Error playing audio: $e'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        }
                      },
                      tooltip: 'Pronunciation',
                    ),
                ],
              ),
            ],
          ),

          // Show pinyin if available - positioned below the text
          if (pinyinText.isNotEmpty) ...[
            const SizedBox(height: DesignSystem.spaceS),
            Text(
              pinyinText,
              style: theme.textTheme.titleMedium?.copyWith(
                fontStyle: FontStyle.italic,
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
              softWrap: true, // Allow pinyin wrapping for long text
            ),
          ],

          // No definition section at all - user has translator and other tabs
        ],
      ),
    );
  }

  /// Build the dictionary view with definitions when entries are found
  Widget _buildDictionaryView() {
    final theme = Theme.of(context);

    // Generate pinyin for the text if it's Chinese
    String pinyinText = '';
    try {
      if (_isChinese(widget.text)) {
        pinyinText = PinyinHelper.getPinyin(
          widget.text,
          separator: ' ',
          format: PinyinFormat.WITH_TONE_MARK,
        );
      }
    } catch (e) {
      AnxLog.warning('Error generating pinyin for dictionary view: $e');
      // Fallback to first entry pinyin if available
      if (_entries.isNotEmpty && _entries.first.pinyin.isNotEmpty) {
        pinyinText = _entries.first.formattedPinyin();
      }
    }

    // Collect all definitions from all entries (including polyphonic variants)
    List<String> allDefinitions = [];
    for (var entry in _entries) {
      allDefinitions.addAll(entry.definitions);
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with selected text and action buttons
          Row(
            children: [
              // Selected text display
              Expanded(
                child: Text(
                  widget.text,
                  style: theme.textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                  softWrap: true,
                ),
              ),

              const SizedBox(width: 12),

              // Compact action buttons
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Brush button (stroke animation practice) - first position
                  if (_isChinese(widget.text))
                    IconButton(
                      icon: Icon(AdaptiveIcons.brush),
                      iconSize: 18,
                      visualDensity: VisualDensity.compact,
                      padding: const EdgeInsets.all(4),
                      constraints:
                          const BoxConstraints(minWidth: 32, minHeight: 32),
                      onPressed: () {
                        // Close the context menu overlay before navigation
                        widget.onClose?.call();
                        Navigator.push(
                          context,
                          MaterialPageRoute<void>(
                            builder: (context) =>
                                BatchCharacterStudyPage(text: widget.text),
                          ),
                        );
                      },
                      tooltip: 'Stroke Order Practice',
                    ),

                  // Copy button - second position
                  IconButton(
                    icon: Icon(AdaptiveIcons.copy),
                    iconSize: 18,
                    visualDensity: VisualDensity.compact,
                    padding: const EdgeInsets.all(4),
                    constraints:
                        const BoxConstraints(minWidth: 32, minHeight: 32),
                    onPressed: () {
                      Clipboard.setData(ClipboardData(text: widget.text));
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(L10n.of(context).notes_page_copied),
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    },
                    tooltip: L10n.of(context).common_copy,
                  ),

                  // Pronunciation button - third position
                  if (_isChinese(widget.text))
                    IconButton(
                      icon: Icon(AdaptiveIcons.volume),
                      iconSize: 18,
                      visualDensity: VisualDensity.compact,
                      padding: const EdgeInsets.all(4),
                      constraints:
                          const BoxConstraints(minWidth: 32, minHeight: 32),
                      onPressed: () async {
                        try {
                          await _onlineDictionaryService
                              .playPronunciation(widget.text, context: context);
                        } catch (e) {
                          if (!mounted) return;
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Error playing audio: $e'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        }
                      },
                      tooltip: 'Pronunciation',
                    ),
                ],
              ),
            ],
          ),

          // Show pinyin if available - positioned below the text
          if (pinyinText.isNotEmpty) ...[
            const SizedBox(height: DesignSystem.spaceS),
            Text(
              pinyinText,
              style: theme.textTheme.titleMedium?.copyWith(
                fontStyle: FontStyle.italic,
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
              softWrap: true,
            ),
          ],

          // Show definitions below pinyin when entries exist
          if (allDefinitions.isNotEmpty) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerLow,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.colorScheme.outlineVariant,
                  width: 1,
                ),
              ),
              child: Text(
                allDefinitions.join(' / '),
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface,
                  height: 1.4,
                  fontSize: 14,
                ),
                softWrap: true,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
